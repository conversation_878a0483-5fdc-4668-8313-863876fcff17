# 文件组织自动化脚本使用指南

## 概述

本项目提供了两套文件组织自动化脚本，分别针对不同的工作模式：

- **TelecommutingMode（远程办公模式）**：适用于远程办公环境
- **LocalOfficeModel（本地办公模式）**：适用于本地办公环境，规则更严格

## 脚本列表

### TelecommutingMode（远程办公模式）
- `scripts/automation/TelecommutingMode-check-file-organization.sh` - 文件组织规范检查
- `scripts/automation/TelecommutingMode-organize-task-files.sh` - 文件自动整理

### LocalOfficeModel（本地办公模式）
- `scripts/automation/LocalOfficeModel-check-file-organization.sh` - 文件组织规范检查（严格模式）
- `scripts/automation/LocalOfficeModel-organize-task-files.sh` - 文件自动整理（增强功能）

## 使用方法

### 检查文件组织规范

**远程办公模式**：
```bash
./scripts/automation/TelecommutingMode-check-file-organization.sh
```

**本地办公模式**：
```bash
./scripts/automation/LocalOfficeModel-check-file-organization.sh
```

### 自动整理文件

**远程办公模式**：
```bash
./scripts/automation/TelecommutingMode-organize-task-files.sh
```

**本地办公模式**：
```bash
./scripts/automation/LocalOfficeModel-organize-task-files.sh
```

## 两种模式的区别

### TelecommutingMode（远程办公模式）
- **特点**：基础的文件组织规范
- **检查项目**：
  - 根目录文件管控
  - Scripts目录结构
  - Docs目录结构
  - 基本命名规范
- **适用场景**：远程办公、多人协作、基础规范要求

### LocalOfficeModel（本地办公模式）
- **特点**：更严格的文件组织规范和增强功能
- **检查项目**：
  - 严格的根目录文件管控
  - 完整的Scripts目录结构（包含security子目录）
  - 扩展的Docs目录结构（包含security、performance、training、fixes子目录）
  - 详细的命名规范检查
  - 脚本权限检查
  - 临时文件清理
  - 智能文件位置建议
- **增强功能**：
  - 自动创建缺失目录
  - 文件冲突处理（自动备份）
  - 权限自动修复
  - 生成详细整理报告
  - 支持中文文件名处理
- **适用场景**：本地开发、严格规范要求、完整项目维护

## 目录结构规范

### Scripts目录结构
```
scripts/
├── automation/     # 自动化脚本
├── database/       # 数据库脚本
├── deployment/     # 部署脚本
├── development/    # 开发脚本
├── performance/    # 性能脚本
├── testing/        # 测试脚本
└── security/       # 安全脚本（LocalOfficeModel）
```

### Docs目录结构
```
docs/
├── architecture/   # 架构文档
├── deployment/     # 部署文档
├── development/    # 开发文档
├── git/           # Git相关文档
├── guides/        # 指南文档
├── reports/       # 报告文档
├── security/      # 安全文档（LocalOfficeModel）
├── performance/   # 性能文档（LocalOfficeModel）
├── training/      # 培训文档（LocalOfficeModel）
└── fixes/         # 修复文档（LocalOfficeModel）
```

## 文件分类规则

### 脚本文件分类
- **自动化脚本**：`*automation*.sh`, `*organize*.sh`, `*check*.sh`, `update-*.sh`, `install-*.sh`
- **数据库脚本**：`*.sql`, `*database*.sh`, `*db*.sh`
- **部署脚本**：`deploy*.sh`, `*deployment*.sh`, `docker*.sh`, `k8s*.sh`
- **测试脚本**：`test-*.sh`, `*test*.sh`, `verify-*.sh`, `*.html`
- **性能脚本**：`*performance*.sh`, `*jvm*.sh`, `*optimization*.sh`
- **安全脚本**：`*security*.sh`, `*auth*.sh`, `*ssl*.sh`（LocalOfficeModel）

### 文档文件分类
- **架构文档**：`*architecture*.md`, `*design*.md`, `system-*.md`
- **开发文档**：`*development*.md`, `*implementation*.md`, `*refactor*.md`
- **部署文档**：`*deployment*.md`, `*deploy*.md`, `docker-*.md`
- **Git文档**：`git-*.md`, `*git*.md`
- **报告文档**：`*report*.md`, `*fix*.md`, `*analysis*.md`, `*summary*.md`
- **指南文档**：`*guide*.md`, `*manual*.md`, `*training*.md`, `*standards*.md`

## 最佳实践

### 日常使用流程
1. **任务开始前**：运行检查脚本确认当前状态
2. **任务完成后**：运行整理脚本自动分类文件
3. **定期维护**：每周运行一次检查脚本

### 推荐工作流
```bash
# 1. 检查当前文件组织状态
./scripts/automation/LocalOfficeModel-check-file-organization.sh

# 2. 如果发现问题，运行整理脚本
./scripts/automation/LocalOfficeModel-organize-task-files.sh

# 3. 再次检查确认整理结果
./scripts/automation/LocalOfficeModel-check-file-organization.sh
```

### 团队协作建议
- **远程团队**：使用TelecommutingMode，保持基础规范
- **本地团队**：使用LocalOfficeModel，维护严格规范
- **混合团队**：根据主要工作模式选择合适的脚本

## 错误处理

### 常见问题

**问题1：脚本没有执行权限**
```bash
chmod +x scripts/automation/*.sh
```

**问题2：目录不存在**
```bash
# LocalOfficeModel会自动创建缺失目录
./scripts/automation/LocalOfficeModel-organize-task-files.sh
```

**问题3：文件冲突**
- LocalOfficeModel会自动备份冲突文件
- 备份文件格式：`原文件名.backup.YYYYMMDD_HHMMSS`

### 脚本返回码
- `0`：成功，无问题
- `1`：有警告，建议处理
- `2`：有错误，必须修复

## 自定义配置

### 修改文件分类规则
编辑脚本中的文件匹配模式：
```bash
# 在organize-task-files.sh中修改
for file in your-pattern*.sh; do
    safe_move_file "$file" "scripts/your-category" "your-description"
done
```

### 添加新的目录类型
在脚本中添加新的目录到相应数组：
```bash
REQUIRED_SCRIPT_DIRS=("automation" "database" "your-new-dir")
REQUIRED_DOC_DIRS=("architecture" "deployment" "your-new-dir")
```

## 报告和日志

### LocalOfficeModel增强功能
- 自动生成整理报告：`docs/reports/file-organization-report-YYYYMMDD_HHMMSS.md`
- 详细的操作日志
- 文件移动统计
- 权限修复记录

### 报告内容
- 整理时间和模式
- 操作统计（创建目录、移动文件、修复权限）
- 遗留文件列表
- 后续建议

## 版本信息

- **创建时间**：2025-01-04
- **版本**：1.0.0
- **兼容性**：Bash 4.0+
- **测试环境**：macOS, Linux

## 支持和反馈

如有问题或建议，请：
1. 检查本文档的错误处理部分
2. 查看脚本生成的报告文件
3. 联系开发团队

---

**注意**：
- TelecommutingMode脚本路径可能在不同电脑上使用，请勿轻易调整
- LocalOfficeModel适用于本地环境，可以根据需要进行优化调整
- 建议在使用前先在测试环境验证脚本功能
