# ============================================================================
# EDC Research Project - Nginx 负载均衡器配置
# 版本: 2.0 (高性能优化版)
# 适用环境: Production
# 创建时间: 2025-07-30
# ============================================================================

# ========== 全局配置优化 ==========
user nginx;
worker_processes auto;                      # 自动检测CPU核心数
worker_rlimit_nofile 65535;                # 工作进程文件描述符限制
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# ========== 事件模块优化 ==========
events {
    worker_connections 8192;               # 每个工作进程的最大连接数
    use epoll;                             # 使用epoll事件模型（Linux）
    multi_accept on;                       # 允许一次接受多个连接
    accept_mutex off;                      # 关闭accept互斥锁
}

# ========== HTTP模块配置 ==========
http {
    # ========== 基础配置 ==========
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # ========== 日志格式优化 ==========
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for" '
                   'rt=$request_time uct="$upstream_connect_time" '
                   'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # ========== 性能优化配置 ==========
    sendfile on;                           # 启用sendfile
    tcp_nopush on;                         # 启用TCP_NOPUSH
    tcp_nodelay on;                        # 启用TCP_NODELAY
    keepalive_timeout 65;                  # 保持连接超时时间
    keepalive_requests 1000;               # 保持连接最大请求数
    types_hash_max_size 2048;              # 类型哈希表最大大小
    server_tokens off;                     # 隐藏Nginx版本信息
    
    # ========== 客户端配置优化 ==========
    client_max_body_size 100M;             # 客户端最大请求体大小
    client_body_buffer_size 128k;          # 客户端请求体缓冲区大小
    client_header_buffer_size 32k;         # 客户端请求头缓冲区大小
    large_client_header_buffers 4 32k;     # 大请求头缓冲区
    client_body_timeout 60s;               # 客户端请求体超时
    client_header_timeout 60s;             # 客户端请求头超时
    send_timeout 60s;                      # 发送超时
    
    # ========== 缓冲区优化 ==========
    proxy_buffering on;                    # 启用代理缓冲
    proxy_buffer_size 8k;                  # 代理缓冲区大小
    proxy_buffers 32 8k;                   # 代理缓冲区数量和大小
    proxy_busy_buffers_size 16k;           # 忙碌缓冲区大小
    proxy_temp_file_write_size 16k;        # 临时文件写入大小
    
    # ========== Gzip压缩优化 ==========
    gzip on;                               # 启用gzip压缩
    gzip_vary on;                          # 启用gzip vary头
    gzip_proxied any;                      # 对所有代理请求启用压缩
    gzip_comp_level 6;                     # 压缩级别（1-9）
    gzip_min_length 1024;                  # 最小压缩长度
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # ========== 连接池配置 ==========
    upstream edc_backend {
        # 负载均衡策略
        least_conn;                        # 最少连接数算法
        
        # 后端服务器配置
        server ************:8100 weight=3 max_fails=3 fail_timeout=30s;
        server ************:8100 weight=3 max_fails=3 fail_timeout=30s;
        server ************:8100 weight=3 max_fails=3 fail_timeout=30s;
        
        # 备用服务器（可选）
        # server ************:8100 backup;
        
        # 连接池配置
        keepalive 64;                      # 保持连接池大小
        keepalive_requests 1000;           # 每个连接的最大请求数
        keepalive_timeout 60s;             # 连接池超时时间
    }
    
    # ========== 健康检查配置（需要nginx-plus或第三方模块） ==========
    # upstream edc_backend_with_health {
    #     least_conn;
    #     server ************:8100 max_fails=3 fail_timeout=30s;
    #     server ************:8100 max_fails=3 fail_timeout=30s;
    #     server ************:8100 max_fails=3 fail_timeout=30s;
    #     
    #     # 健康检查配置
    #     health_check interval=10s fails=3 passes=2 uri=/api/actuator/health;
    # }
    
    # ========== 限流配置 ==========
    # 定义限流区域
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=login_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload_limit:10m rate=5r/s;
    
    # 连接数限制
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;
    
    # ========== 缓存配置 ==========
    # 静态文件缓存
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:100m 
                     max_size=1g inactive=60m use_temp_path=off;
    
    # API响应缓存（可选）
    proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:100m 
                     max_size=500m inactive=10m use_temp_path=off;
    
    # ========== 主服务器配置 ==========
    server {
        listen 80;
        listen [::]:80;
        server_name edc.example.com;
        
        # 重定向到HTTPS（生产环境推荐）
        return 301 https://$server_name$request_uri;
    }
    
    # ========== HTTPS服务器配置 ==========
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name edc.example.com;
        
        # ========== SSL配置 ==========
        ssl_certificate /etc/nginx/ssl/edc.example.com.crt;
        ssl_certificate_key /etc/nginx/ssl/edc.example.com.key;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;
        
        # 现代SSL配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # HSTS配置
        add_header Strict-Transport-Security "max-age=63072000" always;
        
        # ========== 安全头配置 ==========
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
        
        # ========== 限流应用 ==========
        limit_req zone=api_limit burst=200 nodelay;
        limit_conn conn_limit 50;
        
        # ========== 静态文件处理 ==========
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status "STATIC";
            
            # 静态文件缓存
            proxy_cache static_cache;
            proxy_cache_valid 200 1d;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            
            try_files $uri @backend;
        }
        
        # ========== API接口代理 ==========
        location /api/ {
            # 特殊接口限流
            location /api/admin/login {
                limit_req zone=login_limit burst=20 nodelay;
                proxy_pass http://edc_backend;
                include /etc/nginx/proxy_params;
            }
            
            location /api/upload/ {
                limit_req zone=upload_limit burst=10 nodelay;
                client_max_body_size 100M;
                proxy_pass http://edc_backend;
                include /etc/nginx/proxy_params;
                
                # 上传超时配置
                proxy_connect_timeout 60s;
                proxy_send_timeout 300s;
                proxy_read_timeout 300s;
            }
            
            # 默认API代理
            proxy_pass http://edc_backend;
            include /etc/nginx/proxy_params;
        }
        
        # ========== WebSocket支持 ==========
        location /websocket/ {
            proxy_pass http://edc_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket超时配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 3600s;
        }
        
        # ========== 健康检查端点 ==========
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # ========== 监控端点 ==========
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow ***********/24;
            deny all;
        }
        
        # ========== 错误页面 ==========
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
        
        # ========== 后端代理标记 ==========
        @backend {
            proxy_pass http://edc_backend;
            include /etc/nginx/proxy_params;
        }
    }
}

# ============================================================================
# 代理参数配置文件 (/etc/nginx/proxy_params)
# ============================================================================
# proxy_set_header Host $http_host;
# proxy_set_header X-Real-IP $remote_addr;
# proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
# proxy_set_header X-Forwarded-Proto $scheme;
# proxy_set_header X-Forwarded-Host $host;
# proxy_set_header X-Forwarded-Port $server_port;
# 
# # 超时配置
# proxy_connect_timeout 30s;
# proxy_send_timeout 60s;
# proxy_read_timeout 60s;
# 
# # 缓冲配置
# proxy_buffering on;
# proxy_buffer_size 8k;
# proxy_buffers 32 8k;
# proxy_busy_buffers_size 16k;
# 
# # 错误处理
# proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
# proxy_next_upstream_tries 3;
# proxy_next_upstream_timeout 30s;
# 
# # 其他配置
# proxy_redirect off;
# proxy_http_version 1.1;
# proxy_set_header Connection "";

# ============================================================================
# 性能优化说明
# ============================================================================
# 1. 使用least_conn负载均衡算法，适合长连接场景
# 2. 启用keepalive连接池，减少连接建立开销
# 3. 配置gzip压缩，减少传输数据量
# 4. 静态文件缓存，减少后端压力
# 5. 限流配置，防止恶意攻击
# 6. SSL/TLS优化，提高安全性
# 7. 健康检查，自动故障转移
# 8. 监控端点，便于运维监控
