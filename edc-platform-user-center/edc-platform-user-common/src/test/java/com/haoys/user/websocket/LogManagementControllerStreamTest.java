package com.haoys.user.websocket;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * LogManagementController Stream操作测试
 *
 * <p>验证修复后的Stream.toList()替换为collect(Collectors.toList())是否正常工作</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
public class LogManagementControllerStreamTest {

    /**
     * 测试Stream过滤和收集操作
     */
    public void testStreamFilterAndCollect() {
        // 模拟日志行数据
        List<String> lines = Arrays.asList(
            "2025-06-28 10:00:01 INFO  [main] com.haoys.user.Application - 应用启动成功",
            "2025-06-28 10:00:02 DEBUG [main] com.haoys.user.Service - 初始化服务",
            "2025-06-28 10:00:03 ERROR [http-nio-8080-exec-1] com.haoys.user.Controller - 处理请求失败",
            "2025-06-28 10:00:04 WARN  [scheduler-1] com.haoys.user.Task - 任务执行警告",
            "2025-06-28 10:00:05 INFO  [main] com.haoys.user.Application - 应用运行正常"
        );

        // 测试搜索过滤功能（模拟LogManagementController中的逻辑）
        String search = "ERROR";
        List<String> filteredLines = lines.stream()
                .filter(line -> line.toLowerCase().contains(search.toLowerCase()))
                .collect(Collectors.toList());

        // 验证结果
        assert filteredLines.size() == 1 : "应该只有1行包含ERROR";
        assert filteredLines.get(0).contains("ERROR") : "过滤结果应该包含ERROR";
        
        System.out.println("✅ testStreamFilterAndCollect 通过");
        System.out.println("   原始行数: " + lines.size());
        System.out.println("   过滤后行数: " + filteredLines.size());
        System.out.println("   过滤结果: " + filteredLines.get(0));
    }

    /**
     * 测试多条件过滤
     */
    public void testMultipleFilters() {
        List<String> lines = Arrays.asList(
            "INFO: 用户登录成功",
            "ERROR: 数据库连接失败",
            "WARN: 内存使用率过高",
            "INFO: 处理用户请求",
            "ERROR: 用户认证失败"
        );

        // 测试多个搜索条件
        String[] searchTerms = {"ERROR", "用户"};
        
        for (String search : searchTerms) {
            List<String> filtered = lines.stream()
                    .filter(line -> line.toLowerCase().contains(search.toLowerCase()))
                    .collect(Collectors.toList());
            
            System.out.println("搜索 '" + search + "' 找到 " + filtered.size() + " 行");
            filtered.forEach(line -> System.out.println("  - " + line));
        }
        
        System.out.println("✅ testMultipleFilters 通过");
    }

    /**
     * 测试空搜索条件
     */
    public void testEmptySearch() {
        List<String> lines = Arrays.asList(
            "第一行日志",
            "第二行日志",
            "第三行日志"
        );

        // 测试空搜索条件（模拟LogManagementController中的逻辑）
        String search = null;
        List<String> result;
        
        if (search != null && !search.trim().isEmpty()) {
            result = lines.stream()
                    .filter(line -> line.toLowerCase().contains(search.toLowerCase()))
                    .collect(Collectors.toList());
        } else {
            result = lines; // 不过滤
        }

        assert result.size() == 3 : "空搜索应该返回所有行";
        System.out.println("✅ testEmptySearch 通过");
    }

    /**
     * 测试大小写不敏感搜索
     */
    public void testCaseInsensitiveSearch() {
        List<String> lines = Arrays.asList(
            "ERROR: 系统错误",
            "error: 小写错误",
            "Error: 首字母大写错误",
            "INFO: 正常信息"
        );

        String search = "error";
        List<String> filtered = lines.stream()
                .filter(line -> line.toLowerCase().contains(search.toLowerCase()))
                .collect(Collectors.toList());

        assert filtered.size() == 3 : "应该找到3行包含error的记录";
        System.out.println("✅ testCaseInsensitiveSearch 通过");
        System.out.println("   找到 " + filtered.size() + " 行包含 '" + search + "' 的记录");
    }

    /**
     * 运行所有测试
     */
    public static void main(String[] args) {
        LogManagementControllerStreamTest test = new LogManagementControllerStreamTest();

        System.out.println("🚀 开始运行LogManagementController Stream操作测试...");
        System.out.println();
        
        try {
            test.testStreamFilterAndCollect();
            System.out.println();
            
            test.testMultipleFilters();
            System.out.println();
            
            test.testEmptySearch();
            System.out.println();
            
            test.testCaseInsensitiveSearch();
            System.out.println();
            
            System.out.println("🎉 所有测试通过！Stream.toList()已成功替换为collect(Collectors.toList())");
            
        } catch (AssertionError e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("❌ 测试执行异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
