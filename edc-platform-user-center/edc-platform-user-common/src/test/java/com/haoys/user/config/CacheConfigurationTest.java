package com.haoys.user.config;

import com.haoys.user.test.annotation.IntegrationTest;
import com.haoys.user.test.annotation.ConditionalOnTestEnabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存配置测试类
 *
 * <p>测试缓存管理器配置是否正确，确保不会出现CacheManager冲突</p>
 *
 * 使用方法：
 * 1. 条件运行：mvn test -Dtest.enabled=true
 * 2. Profile运行：mvn test -Dspring.profiles.active=test
 * 3. 直接运行：使用@ActiveProfiles("test")自动加载application-test.yml
 * 4. IDE运行：激活test profile或设置VM options: -Dtest.enabled=true
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@IntegrationTest
@ConditionalOnTestEnabled
@ActiveProfiles("test")
@DisplayName("缓存配置测试")
class CacheConfigurationTest {

    @Autowired(required = false)
    private CacheManager cacheManager;

    @Test
    @DisplayName("测试CacheManager是否正确配置")
    void testCacheManagerConfiguration() {
        // 验证CacheManager存在
        assertNotNull(cacheManager, "CacheManager应该被正确配置");
        
        // 验证是RedisCacheManager类型
        assertTrue(cacheManager instanceof RedisCacheManager, 
            "CacheManager应该是RedisCacheManager类型");
    }

    @Test
    @DisplayName("测试预定义缓存是否存在")
    void testPredefinedCaches() {
        if (cacheManager != null) {
            // 测试预定义的缓存名称
            String[] expectedCacheNames = {
                "user-cache",
                "dict-cache", 
                "config-cache",
                "short-cache",
                "long-cache"
            };

            for (String cacheName : expectedCacheNames) {
                assertNotNull(cacheManager.getCache(cacheName), 
                    "缓存 " + cacheName + " 应该存在");
            }
        }
    }

    @Test
    @DisplayName("测试缓存基本功能")
    void testCacheBasicFunctionality() {
        if (cacheManager != null) {
            // 获取用户缓存
            org.springframework.cache.Cache userCache = cacheManager.getCache("user-cache");
            assertNotNull(userCache, "用户缓存应该存在");

            // 测试缓存存储和获取
            String testKey = "test-key";
            String testValue = "test-value";

            userCache.put(testKey, testValue);
            org.springframework.cache.Cache.ValueWrapper cachedValue = userCache.get(testKey);

            assertNotNull(cachedValue, "缓存值应该存在");
            assertEquals(testValue, cachedValue.get(), "缓存值应该正确");

            // 测试缓存清除
            userCache.evict(testKey);
            org.springframework.cache.Cache.ValueWrapper evictedValue = userCache.get(testKey);
            assertNull(evictedValue, "清除后缓存值应该为空");
        }
    }

    @Test
    @DisplayName("测试缓存管理器唯一性")
    void testCacheManagerUniqueness() {
        // 这个测试确保只有一个CacheManager bean
        assertNotNull(cacheManager, "应该有且仅有一个CacheManager");
        
        // 如果有多个CacheManager，Spring会抛出异常，测试会失败
        // 这个测试通过就说明没有CacheManager冲突
    }
}
