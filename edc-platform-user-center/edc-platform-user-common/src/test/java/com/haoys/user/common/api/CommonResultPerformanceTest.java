package com.haoys.user.common.api;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.BeforeEach;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CommonResult 性能测试类
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@DisplayName("CommonResult 性能测试")
class CommonResultPerformanceTest {

    private static final int ITERATIONS = 10000;
    private static final int THREAD_COUNT = 10;

    @BeforeEach
    void setUp() {
        // 预热JVM
        for (int i = 0; i < 1000; i++) {
            CommonResult.success("warmup");
        }
    }

    @Test
    @DisplayName("单线程性能测试")
    void testSingleThreadPerformance() {
        long startTime = System.nanoTime();
        
        for (int i = 0; i < ITERATIONS; i++) {
            CommonResult<String> result = CommonResult.success("test data " + i);
            assertNotNull(result);
            assertTrue(result.isSuccess());
        }
        
        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        double avgTimePerOperation = (double) duration / ITERATIONS / 1_000_000; // 转换为毫秒
        
        System.out.printf("单线程性能测试: %d 次操作，平均每次 %.4f ms%n", ITERATIONS, avgTimePerOperation);
        
        // 断言平均每次操作时间小于1毫秒
        assertTrue(avgTimePerOperation < 1.0, "平均操作时间应小于1毫秒");
    }

    @Test
    @DisplayName("多线程并发性能测试")
    void testMultiThreadPerformance() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        long startTime = System.nanoTime();
        
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadId = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < ITERATIONS / THREAD_COUNT; j++) {
                    CommonResult<String> result = CommonResult.success("thread-" + threadId + "-data-" + j);
                    assertNotNull(result);
                    assertTrue(result.isSuccess());
                }
            }, executor);
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        double avgTimePerOperation = (double) duration / ITERATIONS / 1_000_000; // 转换为毫秒
        
        System.out.printf("多线程性能测试: %d 个线程，%d 次操作，平均每次 %.4f ms%n", 
                         THREAD_COUNT, ITERATIONS, avgTimePerOperation);
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
        
        // 断言平均每次操作时间小于2毫秒（多线程环境下允许稍高）
        assertTrue(avgTimePerOperation < 2.0, "多线程平均操作时间应小于2毫秒");
    }

    @Test
    @DisplayName("内存使用测试")
    void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收
        System.gc();
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建大量对象
        List<CommonResult<String>> results = new ArrayList<>();
        for (int i = 0; i < ITERATIONS; i++) {
            results.add(CommonResult.success("data-" + i, "message-" + i));
        }
        
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = afterMemory - beforeMemory;
        double avgMemoryPerObject = (double) memoryUsed / ITERATIONS;
        
        System.out.printf("内存使用测试: %d 个对象，总内存使用 %d bytes，平均每个对象 %.2f bytes%n", 
                         ITERATIONS, memoryUsed, avgMemoryPerObject);
        
        // 断言平均每个对象内存使用小于5KB（考虑到JVM开销和测试环境差异）
        assertTrue(avgMemoryPerObject < 5120, "平均每个对象内存使用应小于5KB");
        
        // 清理引用，帮助垃圾回收
        results.clear();
    }

    @RepeatedTest(5)
    @DisplayName("重复性能测试")
    void testRepeatedPerformance() {
        long startTime = System.nanoTime();
        
        for (int i = 0; i < 1000; i++) {
            // 测试不同类型的操作
            CommonResult<String> success = CommonResult.success("data");
            CommonResult<String> failed = CommonResult.failed("error");
            CommonResult<String> unauthorized = CommonResult.unauthorized();
            CommonResult<String> forbidden = CommonResult.forbidden();
            
            assertNotNull(success);
            assertNotNull(failed);
            assertNotNull(unauthorized);
            assertNotNull(forbidden);
        }
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        System.out.printf("重复性能测试: 1000 次混合操作，耗时 %.2f ms%n", duration);
        
        // 断言总耗时小于100毫秒
        assertTrue(duration < 100.0, "1000次混合操作应在100毫秒内完成");
    }

    @Test
    @DisplayName("敏感信息过滤性能测试")
    void testSensitiveDataFilteringPerformance() {
        String sensitiveMessage = "用户信息: password=123456, token=abcdef, secret=xyz789, key=secret123";
        
        long startTime = System.nanoTime();
        
        for (int i = 0; i < 1000; i++) {
            CommonResult<String> result = CommonResult.failed(sensitiveMessage);
            assertNotNull(result.getMessage());
            assertFalse(result.getMessage().contains("123456"));
        }
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        System.out.printf("敏感信息过滤性能测试: 1000 次过滤操作，耗时 %.2f ms%n", duration);
        
        // 断言敏感信息过滤不会显著影响性能
        assertTrue(duration < 200.0, "1000次敏感信息过滤应在200毫秒内完成");
    }

    @Test
    @DisplayName("序列化性能测试")
    void testSerializationPerformance() {
        // 创建复杂数据结构
        List<String> complexData = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            complexData.add("item-" + i);
        }
        
        long startTime = System.nanoTime();
        
        for (int i = 0; i < 1000; i++) {
            CommonResult<List<String>> result = CommonResult.success(complexData);
            assertNotNull(result);
            assertEquals(complexData, result.getData());
        }
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        System.out.printf("序列化性能测试: 1000 次复杂对象创建，耗时 %.2f ms%n", duration);
        
        // 断言复杂对象处理性能
        assertTrue(duration < 50.0, "1000次复杂对象创建应在50毫秒内完成");
    }

    @Test
    @DisplayName("Builder模式性能测试")
    void testBuilderPatternPerformance() {
        long startTime = System.nanoTime();
        
        for (int i = 0; i < 10000; i++) {
            CommonResult<String> result = CommonResult.success("data")
                    .withTraceId("trace-" + i);
            assertNotNull(result);
            assertEquals("trace-" + i, result.getTraceId());
        }
        
        long endTime = System.nanoTime();
        double duration = (endTime - startTime) / 1_000_000.0; // 转换为毫秒
        
        System.out.printf("Builder模式性能测试: 10000 次链式调用，耗时 %.2f ms%n", duration);
        
        // 断言Builder模式不会显著影响性能
        assertTrue(duration < 100.0, "10000次Builder操作应在100毫秒内完成");
    }

    @Test
    @DisplayName("线程安全性测试")
    void testThreadSafety() throws InterruptedException {
        final int threadCount = 20;
        final int operationsPerThread = 1000;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    // 测试不同的操作
                    CommonResult<String> result1 = CommonResult.success("data-" + threadId + "-" + j);
                    CommonResult<String> result2 = CommonResult.failed("error-" + threadId + "-" + j);
                    CommonResult<String> result3 = result1.withTraceId("trace-" + threadId + "-" + j);
                    
                    // 验证结果
                    assertNotNull(result1);
                    assertNotNull(result2);
                    assertNotNull(result3);
                    assertTrue(result1.isSuccess());
                    assertTrue(result2.isFailed());
                    assertEquals("trace-" + threadId + "-" + j, result3.getTraceId());
                }
            }, executor);
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));
        
        System.out.printf("线程安全性测试: %d 个线程，每个线程 %d 次操作，全部完成%n", 
                         threadCount, operationsPerThread);
    }

    @Test
    @DisplayName("垃圾回收压力测试")
    void testGarbageCollectionPressure() {
        Runtime runtime = Runtime.getRuntime();
        long initialFreeMemory = runtime.freeMemory();
        
        // 创建大量短生命周期对象
        for (int i = 0; i < 50000; i++) {
            CommonResult<String> result = CommonResult.success("temp-data-" + i);
            // 立即丢弃引用，增加GC压力
        }
        
        // 强制垃圾回收
        System.gc();
        Thread.yield(); // 让GC有机会运行
        
        long finalFreeMemory = runtime.freeMemory();
        long memoryRecovered = finalFreeMemory - initialFreeMemory;
        
        System.out.printf("垃圾回收压力测试: 创建50000个对象后，内存恢复 %d bytes%n", memoryRecovered);
        
        // 验证内存能够被有效回收（允许较大的内存增长，因为GC行为在不同环境下差异很大）
        assertTrue(memoryRecovered > -500 * 1024 * 1024, "内存应该能够被有效回收（允许500MB内存增长）");
    }
}
