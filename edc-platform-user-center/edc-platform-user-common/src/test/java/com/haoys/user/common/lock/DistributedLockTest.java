package com.haoys.user.common.lock;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;

/**
 * 分布式锁测试
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DistributedLockTest {

    @Autowired
    private DistributedLock distributedLock;

    /**
     * 测试基本的锁获取和释放
     */
    @Test
    public void testBasicLockAndUnlock() {
        String lockKey = "test:basic:lock";
        
        // 获取锁
        boolean acquired = distributedLock.tryLock(lockKey);
        assertTrue("应该能够获取锁", acquired);
        
        // 检查锁状态
        assertTrue("锁应该被持有", distributedLock.isLocked(lockKey));
        
        // 释放锁
        distributedLock.unlock(lockKey);
        
        // 检查锁状态
        assertFalse("锁应该被释放", distributedLock.isLocked(lockKey));
    }

    /**
     * 测试可重入锁功能
     */
    @Test
    public void testReentrantLock() {
        String lockKey = "test:reentrant:lock";
        
        // 第一次获取锁
        boolean acquired1 = distributedLock.tryLock(lockKey);
        assertTrue("应该能够获取锁", acquired1);
        
        // 同一线程再次获取锁（可重入）
        boolean acquired2 = distributedLock.tryLock(lockKey);
        assertTrue("同一线程应该能够重入锁", acquired2);
        
        // 释放锁（第一次）
        distributedLock.unlock(lockKey);
        assertTrue("锁应该仍然被持有", distributedLock.isLocked(lockKey));
        
        // 释放锁（第二次）
        distributedLock.unlock(lockKey);
        assertFalse("锁应该被完全释放", distributedLock.isLocked(lockKey));
    }

    /**
     * 测试锁的互斥性
     */
    @Test
    public void testLockMutex() throws InterruptedException {
        String lockKey = "test:mutex:lock";
        AtomicInteger counter = new AtomicInteger(0);
        int threadCount = 5;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    if (distributedLock.tryLock(lockKey, 1, 5, TimeUnit.SECONDS)) {
                        try {
                            // 模拟业务操作
                            int current = counter.get();
                            Thread.sleep(100); // 增加竞争条件
                            counter.set(current + 1);
                        } finally {
                            distributedLock.unlock(lockKey);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        assertTrue("测试超时", latch.await(10, TimeUnit.SECONDS));
        executor.shutdown();
        
        // 由于互斥锁的保护，只有一个线程能成功执行
        assertEquals("只有一个线程应该能够执行", 1, counter.get());
    }

    /**
     * 测试锁超时功能
     */
    @Test
    public void testLockTimeout() throws InterruptedException {
        String lockKey = "test:timeout:lock";
        
        // 获取锁并设置较短的超时时间
        boolean acquired = distributedLock.tryLock(lockKey, 1, 2, TimeUnit.SECONDS);
        assertTrue("应该能够获取锁", acquired);
        
        // 等待锁超时
        Thread.sleep(3000);
        
        // 锁应该已经超时释放
        assertFalse("锁应该已经超时释放", distributedLock.isLocked(lockKey));
    }

    /**
     * 测试获取锁失败的情况
     */
    @Test
    public void testLockAcquisitionFailure() {
        String lockKey = "test:failure:lock";
        
        // 第一个线程获取锁
        boolean acquired1 = distributedLock.tryLock(lockKey, 0, 5, TimeUnit.SECONDS);
        assertTrue("第一个线程应该能够获取锁", acquired1);
        
        try {
            // 第二个线程尝试获取锁（不等待）
            boolean acquired2 = distributedLock.tryLock(lockKey, 0, 5, TimeUnit.SECONDS);
            assertFalse("第二个线程应该获取锁失败", acquired2);
        } finally {
            distributedLock.unlock(lockKey);
        }
    }

    /**
     * 测试强制释放锁功能
     */
    @Test
    public void testForceUnlock() {
        String lockKey = "test:force:unlock";
        
        // 获取锁
        boolean acquired = distributedLock.tryLock(lockKey);
        assertTrue("应该能够获取锁", acquired);
        
        // 强制释放锁
        boolean forceReleased = distributedLock.forceUnlock(lockKey);
        assertTrue("应该能够强制释放锁", forceReleased);
        
        // 检查锁状态
        assertFalse("锁应该被强制释放", distributedLock.isLocked(lockKey));
    }

    /**
     * 测试锁的剩余时间
     */
    @Test
    public void testLockRemainTime() throws InterruptedException {
        String lockKey = "test:remain:time";
        
        // 获取锁，设置10秒超时
        boolean acquired = distributedLock.tryLock(lockKey, 1, 10, TimeUnit.SECONDS);
        assertTrue("应该能够获取锁", acquired);
        
        try {
            // 检查剩余时间
            long remainTime = distributedLock.getRemainTime(lockKey);
            assertTrue("剩余时间应该大于0", remainTime > 0);
            assertTrue("剩余时间应该小于等于10秒", remainTime <= 10);
            
            // 等待1秒
            Thread.sleep(1000);
            
            // 再次检查剩余时间
            long remainTime2 = distributedLock.getRemainTime(lockKey);
            assertTrue("剩余时间应该减少", remainTime2 < remainTime);
            
        } finally {
            distributedLock.unlock(lockKey);
        }
    }
}
