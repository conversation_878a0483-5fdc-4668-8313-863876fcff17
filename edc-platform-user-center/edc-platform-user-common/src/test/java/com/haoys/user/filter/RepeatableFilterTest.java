package com.haoys.user.filter;

import com.haoys.user.common.filter.RepeatableFilter;
import com.haoys.user.common.filter.RepeatedlyRequestWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletException;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RepeatableFilter测试类
 *
 * <p>测试RepeatableFilter对不同类型请求的处理</p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public class RepeatableFilterTest {

    private RepeatableFilter filter;
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private MockFilterChain filterChain;

    @BeforeEach
    void setUp() {
        filter = new RepeatableFilter();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filterChain = new MockFilterChain();
    }

    @Test
    void testJsonRequestShouldBeWrapped() throws ServletException, IOException {
        // 设置JSON请求
        request.setContentType(MediaType.APPLICATION_JSON_VALUE);
        request.setContent("{\"test\": \"data\"}".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用，并且请求被包装
        assertNotNull(filterChain.getRequest());
        assertTrue(filterChain.getRequest() instanceof RepeatedlyRequestWrapper);
    }

    @Test
    void testMultipartRequestShouldNotBeWrapped() throws ServletException, IOException {
        // 设置multipart请求
        request.setContentType(MediaType.MULTIPART_FORM_DATA_VALUE);
        request.setContent("multipart data".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证原始请求被传递，没有被包装
        assertEquals(request, filterChain.getRequest());
    }

    @Test
    void testMultipartFormDataWithBoundaryShouldNotBeWrapped() throws ServletException, IOException {
        // 设置带boundary的multipart请求
        request.setContentType("multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
        request.setContent("multipart data with boundary".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证原始请求被传递，没有被包装
        assertEquals(request, filterChain.getRequest());
    }

    @Test
    void testPlainTextRequestShouldNotBeWrapped() throws ServletException, IOException {
        // 设置普通文本请求
        request.setContentType(MediaType.TEXT_PLAIN_VALUE);
        request.setContent("plain text data".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证原始请求被传递，没有被包装
        assertEquals(request, filterChain.getRequest());
    }

    @Test
    void testNullContentTypeShouldNotBeWrapped() throws ServletException, IOException {
        // 不设置Content-Type
        request.setContent("some data".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证原始请求被传递，没有被包装
        assertEquals(request, filterChain.getRequest());
    }

    @Test
    void testEmptyContentTypeShouldNotBeWrapped() throws ServletException, IOException {
        // 设置空的Content-Type
        request.setContentType("");
        request.setContent("some data".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证原始请求被传递，没有被包装
        assertEquals(request, filterChain.getRequest());
    }

    @Test
    void testJsonWithCharsetShouldBeWrapped() throws ServletException, IOException {
        // 设置带字符集的JSON请求
        request.setContentType("application/json; charset=UTF-8");
        request.setContent("{\"test\": \"data\"}".getBytes());

        // 执行过滤器
        filter.doFilter(request, response, filterChain);

        // 验证过滤器链被调用
        assertNotNull(filterChain.getRequest());
    }
}
