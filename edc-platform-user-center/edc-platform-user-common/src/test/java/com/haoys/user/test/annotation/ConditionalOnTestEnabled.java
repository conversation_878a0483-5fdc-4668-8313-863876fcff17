package com.haoys.user.test.annotation;

import org.junit.jupiter.api.condition.EnabledIfSystemProperty;

import java.lang.annotation.*;

/**
 * 条件测试注解
 * 
 * <p>只有在系统属性test.enabled=true时才执行测试</p>
 * 
 * 使用方法：
 * 1. 在测试类或方法上添加 @ConditionalOnTestEnabled
 * 2. 运行时设置系统属性：-Dtest.enabled=true
 * 3. 或者在IDE中设置VM options：-Dtest.enabled=true
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@EnabledIfSystemProperty(named = "test.enabled", matches = "true")
public @interface ConditionalOnTestEnabled {
}
