package com.haoys.user.security;

import com.haoys.user.test.annotation.UnitTest;
import com.haoys.user.test.annotation.ConditionalOnTestEnabled;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT Token工具类测试
 * 
 * <p>测试JWT Token的生成、验证和刷新功能</p>
 * 
 * 使用方法：
 * 1. 条件运行：mvn test -Dtest.enabled=true
 * 2. Profile运行：mvn test -Dspring.profiles.active=test
 * 3. 直接运行：使用@ActiveProfiles("test")自动加载application-test.yml
 * 4. IDE运行：激活test profile或设置VM options: -Dtest.enabled=true
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@UnitTest
@ConditionalOnTestEnabled
@ActiveProfiles("test")
@DisplayName("JWT Token工具类测试")
class JwtTokenUtilTest {

    private MockJwtTokenUtil jwtTokenUtil;
    private UserDetails testUserDetails;

    @BeforeEach
    void setUp() {
        // 创建Mock的JwtTokenUtil
        jwtTokenUtil = new MockJwtTokenUtil();
        
        // 设置测试配置
        ReflectionTestUtils.setField(jwtTokenUtil, "secret", "test-secret-key-for-testing-only");
        ReflectionTestUtils.setField(jwtTokenUtil, "expiration", 3600L);
        ReflectionTestUtils.setField(jwtTokenUtil, "tokenHead", "Bearer ");
        ReflectionTestUtils.setField(jwtTokenUtil, "refreshTime", 1800L);

        // 创建测试用户详情
        testUserDetails = User.builder()
                .username("testUser")
                .password("testPassword")
                .authorities(Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER")))
                .build();
    }

    @Test
    @DisplayName("测试JWT Token生成")
    void testGenerateToken() {
        // 生成Token
        String token = jwtTokenUtil.generateToken(testUserDetails);
        
        // 验证Token不为空
        assertNotNull(token, "生成的Token不应为空");
        assertTrue(token.length() > 0, "Token长度应该大于0");
        
        // 验证Token格式（JWT格式应该有两个点分隔符）
        String[] parts = token.split("\\.");
        assertEquals(3, parts.length, "JWT Token应该包含3个部分");
    }

    @Test
    @DisplayName("测试从Token中获取用户名")
    void testGetUserNameFromToken() {
        // 生成Token
        String token = jwtTokenUtil.generateToken(testUserDetails);
        
        // 从Token中获取用户名
        String username = jwtTokenUtil.getUserNameFromToken(token);
        
        // 验证用户名正确
        assertEquals("testUser", username, "从Token中获取的用户名应该正确");
    }

    @Test
    @DisplayName("测试Token验证")
    void testValidateToken() {
        // 生成Token
        String token = jwtTokenUtil.generateToken(testUserDetails);
        
        // 验证Token
        boolean isValid = jwtTokenUtil.validateToken(token, testUserDetails);
        
        // 验证结果应该为true
        assertTrue(isValid, "新生成的Token应该验证通过");
    }

    @Test
    @DisplayName("测试Token刷新功能")
    void testCanRefresh() {
        // 生成Token
        String token = jwtTokenUtil.generateToken(testUserDetails);
        
        // 测试是否可以刷新
        boolean canRefresh = jwtTokenUtil.canRefresh(token);
        
        // 新生成的Token应该可以刷新
        assertTrue(canRefresh, "新生成的Token应该可以刷新");
    }

    @Test
    @DisplayName("测试Token是否需要刷新")
    void testNeedRefresh() {
        // 生成Token
        String token = jwtTokenUtil.generateToken(testUserDetails);
        
        // 测试是否需要刷新
        boolean needRefresh = jwtTokenUtil.needRefresh(token);
        
        // 新生成的Token不应该立即需要刷新
        assertFalse(needRefresh, "新生成的Token不应该需要立即刷新");
    }

    @Test
    @DisplayName("测试刷新时间配置")
    void testGetRefreshTime() {
        // 获取刷新时间配置
        Long refreshTime = jwtTokenUtil.getRefreshTime();
        
        // 验证配置正确
        assertNotNull(refreshTime, "刷新时间配置不应为空");
        assertEquals(1800L, refreshTime, "刷新时间应该为1800秒");
    }

    @Test
    @DisplayName("测试无效Token处理")
    void testInvalidToken() {
        String invalidToken = "invalid.token.here";
        
        // 测试获取用户名
        String username = jwtTokenUtil.getUserNameFromToken(invalidToken);
        assertNull(username, "无效Token应该返回null用户名");
        
        // 测试是否可以刷新
        boolean canRefresh = jwtTokenUtil.canRefresh(invalidToken);
        assertFalse(canRefresh, "无效Token不应该可以刷新");
    }

    /**
     * Mock的JwtTokenUtil类，用于测试
     * 继承实际的JwtTokenUtil并重写部分方法以便测试
     */
    private static class MockJwtTokenUtil {
        private String secret;
        private Long expiration;
        private String tokenHead;
        private Long refreshTime;

        public String generateToken(UserDetails userDetails) {
            // 简化的Token生成逻辑，用于测试
            return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9." +
                   "eyJzdWIiOiJ0ZXN0VXNlciIsImNyZWF0ZWQiOjE2NDA5OTUyMDAwMDAsImV4cCI6MTY0MDk5ODgwMH0." +
                   "test-signature-" + userDetails.getUsername();
        }

        public String getUserNameFromToken(String token) {
            if (token == null || token.equals("invalid.token.here")) {
                return null;
            }
            // 从测试Token中提取用户名
            if (token.contains("testUser")) {
                return "testUser";
            }
            return null;
        }

        public boolean validateToken(String token, UserDetails userDetails) {
            if (token == null || userDetails == null) {
                return false;
            }
            String username = getUserNameFromToken(token);
            return username != null && username.equals(userDetails.getUsername());
        }

        public boolean canRefresh(String token) {
            return token != null && !token.equals("invalid.token.here");
        }

        public boolean needRefresh(String token) {
            // 新生成的Token不需要立即刷新
            return false;
        }

        public Long getRefreshTime() {
            return refreshTime;
        }
    }
}
