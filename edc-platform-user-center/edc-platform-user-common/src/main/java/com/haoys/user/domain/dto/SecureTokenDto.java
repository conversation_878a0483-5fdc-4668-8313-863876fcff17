package com.haoys.user.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 安全Token数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@Accessors(chain = true)
public class SecureTokenDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 应用ID
     */
    private String appId;

    /**
     * 环境标识
     */
    private String environment;

    /**
     * 生成的code
     */
    private String code;

    /**
     * 刷新码
     */
    private String refreshCode;

    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 是否已使用
     */
    private Boolean used;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedTime;
    
    /**
     * 关联的用户ID（可选）
     */
    private String userId;
    
    /**
     * 扩展信息（可选）
     */
    private String extraInfo;
}
