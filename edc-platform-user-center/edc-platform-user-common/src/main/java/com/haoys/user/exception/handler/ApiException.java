package com.haoys.user.exception.handler;

import com.haoys.user.common.api.BaseResultCode;

/**
 * 自定义API异常
 */
public class ApiException extends RuntimeException {
    private BaseResultCode errorCode;

    public ApiException(BaseResultCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public ApiException(String message) {
        super(message);
    }

    public ApiException(Throwable cause) {
        super(cause);
    }

    public ApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseResultCode getErrorCode() {
        return errorCode;
    }
}
