package com.haoys.user.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.ObjectRecord;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.stream.StreamReceiver;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.Duration;

@Service
public class MessageConsumerService {
    
    @Autowired
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;
    
    //@Retryable(maxAttempts = 3, backoff = @Backoff(delay = 0, multiplier = 1, maxDelay = 1000))
    public Flux<ObjectRecord<String, String>> consumeMessages(String group, String stream, String consumer) {
        StreamReceiver.StreamReceiverOptions<String, ObjectRecord<String, String>> options =
                StreamReceiver.StreamReceiverOptions.builder()
                        .pollTimeout(Duration.ofMillis(100))
                        .targetType(String.class)
                        .build();
        StreamReceiver<String, ObjectRecord<String, String>> receiver = StreamReceiver.create(reactiveRedisTemplate.getConnectionFactory(), options);
        Flux<ObjectRecord<String, String>> messages = receiver.receive(StreamOffset.fromStart(stream))
                .map(record -> {
                    // 模拟消息处理失败
                    if (record.getValue().equals("fail")) {
                        throw new RuntimeException("Failed to process message");
                    }
                    return record;
                })
                .flatMap(record -> {
                    // 手动确认消息处理
                    return reactiveRedisTemplate.opsForStream()
                            .acknowledge(stream, group, record.getId())
                            .thenReturn(record);
                });
        //.retryBackoff(3, Duration.ofSeconds(1)); // 添加重试机制，最多重试3次，每次间隔1秒
        return messages;
    }
}

