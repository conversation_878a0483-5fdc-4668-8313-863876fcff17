package com.haoys.user.storge.cloud;

import com.haoys.user.enums.system.OssTypeEnum;
import com.haoys.user.common.util.SpringContextUtils;

/**
 * 文件上传Factory
 */
public final class OssStorageFactory {

    private static OssStorageConfig config = null;

    static {
        config = SpringContextUtils.getBean(OssStorageConfig.class);
    }


    public static OssStorageService build() {
        if (config.getOssType() == OssTypeEnum.QINIU) {
            return new QiniuCloudStorageService(config);
        } else if (config.getOssType() == OssTypeEnum.ALIYUN) {
            return new AliyunOssStorageService(config);
        } else if (config.getOssType() == OssTypeEnum.UPYUN) {
            return new UpyunStorageService(config);
        } else if (config.getOssType() == OssTypeEnum.LOCAL) {
            return new localStorageService(config);
        }else if (config.getOssType() == OssTypeEnum.MINIO) {
            return new MIniOStorageService(config);
        }
        return null;
    }

}
