package com.haoys.user.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 平台OSS配置
 * 
 * <p>包含对象存储服务和Swagger文档的配置</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "platform")
public class PlatformOssConfig {

    /**
     * 对象存储服务配置
     */
    private OssConfig oss = new OssConfig();

    /**
     * API文档配置
     */
    private SwaggerConfig swagger = new SwaggerConfig();

    public static class OssConfig {
        /**
         * OSS类型（1=阿里云OSS，2=腾讯云COS，3=本地存储）
         */
        private int ossType = 1;

        /**
         * OSS服务端点
         */
        private String endpoint;

        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥Secret
         */
        private String accessKeySecret;

        /**
         * 存储桶名称
         */
        private String bucketName;

        /**
         * 根路径
         */
        private String rootPath;

        /**
         * 访问域名
         */
        private String domain;

        /**
         * 本地上传目录
         */
        private String uploadFolder = "/webserver/upload/";

        /**
         * 文件访问路径模式
         */
        private String accessPathPattern = "/file/**";

        /**
         * 文件预览URL
         */
        private String viewUrl;

        /**
         * 文件大小限制（MB）
         */
        private int maxFileSize = 100;

        /**
         * 允许的文件类型
         */
        private String allowedTypes = "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar";

        /**
         * 是否启用文件类型检查
         */
        private boolean enableTypeCheck = true;

        /**
         * 是否启用文件大小检查
         */
        private boolean enableSizeCheck = true;

        /**
         * 上传文件名是否使用UUID
         */
        private boolean useUuidFileName = true;

        /**
         * 是否保留原始文件名
         */
        private boolean keepOriginalFileName = false;

        /**
         * 获取允许的文件类型列表
         */
        public List<String> getAllowedTypesList() {
            return Arrays.asList(allowedTypes.toLowerCase().split(","));
        }

        /**
         * 检查文件类型是否允许
         */
        public boolean isAllowedType(String fileExtension) {
            if (!enableTypeCheck) {
                return true;
            }
            return getAllowedTypesList().contains(fileExtension.toLowerCase());
        }

        /**
         * 检查文件大小是否允许
         */
        public boolean isAllowedSize(long fileSizeInBytes) {
            if (!enableSizeCheck) {
                return true;
            }
            long maxSizeInBytes = maxFileSize * 1024 * 1024L; // 转换为字节
            return fileSizeInBytes <= maxSizeInBytes;
        }

        // Getters and Setters
        public int getOssType() {
            return ossType;
        }

        public void setOssType(int ossType) {
            this.ossType = ossType;
        }

        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getAccessKeyId() {
            return accessKeyId;
        }

        public void setAccessKeyId(String accessKeyId) {
            this.accessKeyId = accessKeyId;
        }

        public String getAccessKeySecret() {
            return accessKeySecret;
        }

        public void setAccessKeySecret(String accessKeySecret) {
            this.accessKeySecret = accessKeySecret;
        }

        public String getBucketName() {
            return bucketName;
        }

        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }

        public String getRootPath() {
            return rootPath;
        }

        public void setRootPath(String rootPath) {
            this.rootPath = rootPath;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getUploadFolder() {
            return uploadFolder;
        }

        public void setUploadFolder(String uploadFolder) {
            this.uploadFolder = uploadFolder;
        }

        public String getAccessPathPattern() {
            return accessPathPattern;
        }

        public void setAccessPathPattern(String accessPathPattern) {
            this.accessPathPattern = accessPathPattern;
        }

        public String getViewUrl() {
            return viewUrl;
        }

        public void setViewUrl(String viewUrl) {
            this.viewUrl = viewUrl;
        }

        public int getMaxFileSize() {
            return maxFileSize;
        }

        public void setMaxFileSize(int maxFileSize) {
            this.maxFileSize = maxFileSize;
        }

        public String getAllowedTypes() {
            return allowedTypes;
        }

        public void setAllowedTypes(String allowedTypes) {
            this.allowedTypes = allowedTypes;
        }

        public boolean isEnableTypeCheck() {
            return enableTypeCheck;
        }

        public void setEnableTypeCheck(boolean enableTypeCheck) {
            this.enableTypeCheck = enableTypeCheck;
        }

        public boolean isEnableSizeCheck() {
            return enableSizeCheck;
        }

        public void setEnableSizeCheck(boolean enableSizeCheck) {
            this.enableSizeCheck = enableSizeCheck;
        }

        public boolean isUseUuidFileName() {
            return useUuidFileName;
        }

        public void setUseUuidFileName(boolean useUuidFileName) {
            this.useUuidFileName = useUuidFileName;
        }

        public boolean isKeepOriginalFileName() {
            return keepOriginalFileName;
        }

        public void setKeepOriginalFileName(boolean keepOriginalFileName) {
            this.keepOriginalFileName = keepOriginalFileName;
        }
    }

    public static class SwaggerConfig {
        /**
         * 启用Swagger文档
         */
        private boolean enable = true;

        /**
         * 文档标题
         */
        private String title = "EDC科研协作平台API文档";

        /**
         * 文档描述
         */
        private String description = "提供完整的API接口文档和在线测试功能";

        /**
         * 版本号
         */
        private String version = "2.0.0";

        /**
         * 联系信息
         */
        private ContactInfo contact = new ContactInfo();

        public static class ContactInfo {
            private String name = "技术支持团队";
            private String email = "<EMAIL>";
            private String url = "https://www.haoyisheng.com";

            // Getters and Setters
            public String getName() { return name; }
            public void setName(String name) { this.name = name; }
            public String getEmail() { return email; }
            public void setEmail(String email) { this.email = email; }
            public String getUrl() { return url; }
            public void setUrl(String url) { this.url = url; }
        }

        // Getters and Setters
        public boolean isEnable() { return enable; }
        public void setEnable(boolean enable) { this.enable = enable; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        public ContactInfo getContact() { return contact; }
        public void setContact(ContactInfo contact) { this.contact = contact; }
    }

    // Getters and Setters
    public OssConfig getOss() {
        return oss;
    }

    public void setOss(OssConfig oss) {
        this.oss = oss;
    }

    public SwaggerConfig getSwagger() {
        return swagger;
    }

    public void setSwagger(SwaggerConfig swagger) {
        this.swagger = swagger;
    }
}
