package com.haoys.user.exception;

import com.haoys.user.common.api.BaseResultCode;
import com.haoys.user.common.api.ResultCode;

/**
 * 业务异常
 */
public final class ServiceException extends RuntimeException {

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     * <p>
     * 和  一致的设计
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServiceException() {
    }

    public ServiceException(String message) {
        this.message = message;
    }

    public ServiceException(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public ServiceException(ResultCode resultCode) {
        this.code = String.valueOf(resultCode.getCode());
        this.message = resultCode.getMessage();
    }

    public ServiceException(BaseResultCode resultCode) {
        this.code = String.valueOf(resultCode.getCode());
        this.message = resultCode.getMessage();
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public String getMessage() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public ServiceException setMessage(String message) {
        this.message = message;
        return this;
    }

    public ServiceException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }
}
