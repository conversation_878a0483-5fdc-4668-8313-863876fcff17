package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * Redis数据管理请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "Redis数据管理请求参数")
public class RedisManagementParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 秘钥验证参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "秘钥验证参数")
    public static class SecretVerifyParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "配置秘钥", required = true, example = "EDC-REDIS-MANAGEMENT-SECRET-2025")
        private String secretKey;
    }

    /**
     * 获取AccessToken参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "获取AccessToken参数")
    public static class GetAccessTokenParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "验证码", required = true, example = "code_abc123")
        private String code;
        
        @ApiModelProperty(value = "刷新码", required = true, example = "refresh_xyz789")
        private String refreshCode;
    }

    /**
     * Key查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "Key查询参数")
    public static class KeyQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "Redis键", required = true, example = "user:info:123")
        private String key;
    }

    /**
     * 模糊查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "模糊查询参数")
    public static class PatternQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "匹配模式", required = true, example = "user:*")
        private String pattern;
        
        @ApiModelProperty(value = "限制数量", example = "100")
        private Integer limit = 100;
    }

    /**
     * 前缀查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "前缀查询参数")
    public static class PrefixQueryParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "前缀", required = true, example = "user:")
        private String prefix;
        
        @ApiModelProperty(value = "限制数量", example = "100")
        private Integer limit = 100;
    }

    /**
     * 删除Key参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "删除Key参数")
    public static class DeleteKeyParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "Redis键", required = true, example = "user:info:123")
        private String key;
    }

    /**
     * 批量删除参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "批量删除参数")
    public static class BatchDeleteParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "Redis键列表", required = true)
        private List<String> keys;
    }

    /**
     * 模糊删除参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "模糊删除参数")
    public static class PatternDeleteParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "匹配模式", required = true, example = "user:*")
        private String pattern;
    }

    /**
     * 前缀删除参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "前缀删除参数")
    public static class PrefixDeleteParam implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "前缀", required = true, example = "user:")
        private String prefix;
    }

    /**
     * AuthCode查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "AuthCode查询参数")
    public static class AuthCodeQueryParam implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "授权码", required = true, example = "AUTH_CODE_123456")
        private String authCode;

        @ApiModelProperty(value = "页码", example = "1")
        private Integer page = 1;

        @ApiModelProperty(value = "每页大小", example = "20")
        private Integer size = 20;
    }

    /**
     * 分页查询参数
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "分页查询参数")
    public static class PageQueryParam implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "查询类型", required = true, example = "prefix")
        private String queryType;

        @ApiModelProperty(value = "查询条件", required = true, example = "user:")
        private String queryCondition;

        @ApiModelProperty(value = "页码", example = "1")
        private Integer page = 1;

        @ApiModelProperty(value = "每页大小", example = "20")
        private Integer size = 20;
    }
}
