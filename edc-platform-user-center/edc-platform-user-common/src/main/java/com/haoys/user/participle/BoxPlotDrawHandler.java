package com.haoys.user.participle;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class BoxPlotDrawHandler {


    /**
     * 箱形图
     * @param data
     */
    public static BoxPlot plot(double[] data) {
        List<Double> collect = Arrays.stream(data).boxed().sorted().distinct().collect(Collectors.toList());
        data = collect.stream().mapToDouble(i -> i).toArray();

        //中位数
        double median;
        double min;
        double max;
        //下四分位数  0.25
        double Q1;
        //上四分位数 0.75
        double Q3;
        //四分位距
        double IQR;
        //温和异常
        double[] mildOutlier;
        //极端异常
        double[] extremeOutlier;

        if (data.length % 2 == 0) {
            median = (data[(data.length) / 2 - 1] + data[(data.length) / 2]) / 2;
            Q1 = (data[(data.length) / 4 - 1] + data[(data.length) / 4]) / 2;
            Q3 = (data[((data.length) * 3) / 4 - 1] + data[((data.length) * 3) / 4]) / 2;
        } else {
            median = data[(data.length) / 2];
            Q1 = data[(data.length) / 4];
            Q3 = data[(data.length * 3) / 4];
        }
        //最大值
        max = data[data.length - 1];
        //最小值
        min = data[0];
        IQR = Q3 - Q1;

        //内限
        double maxInRegion = Q3 + 1.5 * IQR;
        double mixInRegion = Q1 - 1.5 * IQR;
        //外限
        double maxOutRegion = Q3 + 3 * IQR;
        double mixOutRegion = Q1 - 3 * IQR;

        BoxPlot box = new BoxPlot();
        box.setMin(min);
        box.setQ1(Q1);
        box.setMedian(median);
        box.setQ3(Q3);
        box.setMax(max);
        box.setMixInRegion(mixInRegion);
        box.setMaxInRegion(maxInRegion);
        box.setMixOutRegion(mixOutRegion);
        box.setMaxOutRegion(maxOutRegion);
        box.setIQR(IQR);
        return box;
    }




}
