package com.haoys.user.exception;

import com.haoys.user.common.api.BaseResultCode;
import com.haoys.user.common.api.ResultCode;

/**
 * 业务异常
 */
public final class CustomerException extends RuntimeException {

    /**
     * 错误码
     */
    private int code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     * <p>
     * 和  一致的设计
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public CustomerException() {
    }

    public CustomerException(String message) {
        this.message = message;
    }

    public CustomerException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public CustomerException(ResultCode resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public CustomerException(BaseResultCode resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }

    public CustomerException setMessage(String message) {
        this.message = message;
        return this;
    }

    public CustomerException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }
}
