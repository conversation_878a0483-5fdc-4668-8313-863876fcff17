package com.haoys.user.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态域名配置管理器
 * 根据请求域名动态返回对应的配置信息
 * 
 * <AUTHOR>
 * @since 2025-08-02
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "dynamic-domain")
public class DynamicDomainConfig {

    /**
     * 是否启用动态域名检测
     */
    private boolean enabled = true;

    /**
     * 默认配置
     */
    private DomainSettings defaultSettings = new DomainSettings();

    /**
     * 域名配置映射
     */
    private Map<String, DomainSettings> domains = new HashMap<>();

    /**
     * 初始化默认域名配置
     */
    @PostConstruct
    public void initDefaultDomains() {
        log.info("开始初始化动态域名配置...");
        log.info("当前配置状态: enabled={}, domains.size()={}", enabled, domains.size());

        // 补充缺失的域名配置
        if (!domains.containsKey("dev-1.info.com")) {
            log.info("添加PC端域名配置: dev-1.info.com");
            DomainSettings pcSettings = new DomainSettings();
            pcSettings.setName("PC端");
            pcSettings.setClientType("pc");
            pcSettings.setTheme("desktop");
            pcSettings.setLayout("full");
            pcSettings.getCustomConfig().put("sidebar", true);
            pcSettings.getCustomConfig().put("toolbar", "full");
            domains.put("dev-1.info.com", pcSettings);
        }

        if (!domains.containsKey("dev-2.info.com")) {
            log.info("添加移动端域名配置: dev-2.info.com");
            DomainSettings mobileSettings = new DomainSettings();
            mobileSettings.setName("移动H5端");
            mobileSettings.setClientType("mobile");
            mobileSettings.setTheme("mobile");
            mobileSettings.setLayout("compact");
            mobileSettings.getCustomConfig().put("sidebar", false);
            mobileSettings.getCustomConfig().put("toolbar", "minimal");
            domains.put("dev-2.info.com", mobileSettings);
        }

        if (!domains.containsKey("127.0.0.1")) {
            log.info("添加127.0.0.1域名配置");
            DomainSettings devSettings = new DomainSettings();
            devSettings.setName("本地IP");
            devSettings.setClientType("dev");
            devSettings.setTheme("debug");
            devSettings.setLayout("full");
            devSettings.getCustomConfig().put("debug", true);
            devSettings.getCustomConfig().put("sidebar", true);
            domains.put("127.0.0.1", devSettings);
        }

        log.info("动态域名配置初始化完成，共配置 {} 个域名", domains.size());
        domains.forEach((domain, settings) ->
            log.info("域名配置: {} -> {} ({})", domain, settings.getName(), settings.getClientType()));
    }

    /**
     * 根据请求获取域名配置
     */
    public DomainSettings getDomainSettings(HttpServletRequest request) {
        if (!enabled) {
            log.debug("动态域名检测已禁用，使用默认配置");
            return defaultSettings;
        }

        String serverName = extractServerName(request);
        DomainSettings settings = domains.get(serverName);
        
        if (settings != null) {
            log.debug("找到域名配置: {} -> {}", serverName, settings.getName());
            return settings;
        } else {
            log.debug("未找到域名 {} 的配置，使用默认配置", serverName);
            return defaultSettings;
        }
    }

    /**
     * 构建动态API基础URL
     */
    public String buildApiBaseUrl(HttpServletRequest request) {
        DomainSettings settings = getDomainSettings(request);
        
        String scheme = extractScheme(request);
        String serverName = extractServerName(request);
        int serverPort = extractServerPort(request);
        
        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);
        
        // 根据配置决定是否添加端口
        if (settings.isIncludePort() && shouldIncludePort(scheme, serverPort)) {
            url.append(":").append(serverPort);
        }
        
        // 添加API路径
        String apiPath = settings.getApiPath();
        if (!apiPath.startsWith("/")) {
            url.append("/");
        }
        url.append(apiPath);
        
        if (!apiPath.endsWith("/")) {
            url.append("/");
        }
        
        String result = url.toString();
        log.info("为域名 {} 构建API基础URL: {} (配置: {})", serverName, result, settings.getName());
        return result;
    }

    /**
     * 获取客户端配置信息
     */
    public Map<String, Object> getClientConfig(HttpServletRequest request) {
        DomainSettings settings = getDomainSettings(request);
        String apiBaseUrl = buildApiBaseUrl(request);
        
        Map<String, Object> config = new HashMap<>();
        config.put("apiBaseUrl", apiBaseUrl);
        config.put("domainName", extractServerName(request));
        config.put("configName", settings.getName());
        config.put("clientType", settings.getClientType());
        config.put("platform", settings.getPlatform());
        config.put("theme", settings.getTheme());
        config.put("layout", settings.getLayout());
        config.put("features", settings.getFeatures());
        config.put("apiPath", settings.getApiPath());
        config.put("includePort", settings.isIncludePort());
        config.put("customConfig", settings.getCustomConfig());
        
        return config;
    }

    /**
     * 提取服务器名称（处理代理）
     */
    public String extractServerName(HttpServletRequest request) {
        String forwardedHost = request.getHeader("X-Forwarded-Host");
        if (forwardedHost != null && !forwardedHost.trim().isEmpty()) {
            return forwardedHost.trim().split(",")[0].trim();
        }
        
        String hostHeader = request.getHeader("Host");
        if (hostHeader != null && !hostHeader.trim().isEmpty()) {
            return hostHeader.trim().split(":")[0];
        }
        
        return request.getServerName();
    }

    /**
     * 提取协议（处理代理）
     */
    private String extractScheme(HttpServletRequest request) {
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        if (forwardedProto != null && !forwardedProto.trim().isEmpty()) {
            return forwardedProto.trim().split(",")[0].trim();
        }
        return request.getScheme();
    }

    /**
     * 提取端口（处理代理）
     */
    private int extractServerPort(HttpServletRequest request) {
        String forwardedPort = request.getHeader("X-Forwarded-Port");
        if (forwardedPort != null && !forwardedPort.trim().isEmpty()) {
            try {
                return Integer.parseInt(forwardedPort.trim().split(",")[0].trim());
            } catch (NumberFormatException e) {
                log.warn("无效的X-Forwarded-Port: {}", forwardedPort);
            }
        }
        return request.getServerPort();
    }

    /**
     * 判断是否需要在URL中包含端口号
     */
    private boolean shouldIncludePort(String scheme, int port) {
        return !("http".equals(scheme) && port == 80) && 
               !("https".equals(scheme) && port == 443) &&
               port > 0;
    }

    /**
     * 域名配置类
     */
    @Data
    public static class DomainSettings {
        private String name = "默认配置";
        private String apiPath = "/api/";
        private boolean includePort = false;
        private String clientType = "web";
        private String platform = "web";
        private String theme = "default";
        private String layout = "standard";
        private String features = "basic";
        private Map<String, Object> customConfig = new HashMap<>();
    }
}
