package com.haoys.user.websocket;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 日志消息队列
 *
 * <p>用于缓存日志消息，支持进程内日志和文件日志的队列管理</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Slf4j
public class LoggerQueue {

    // 队列大小
    public static final int QUEUE_MAX_SIZE = 10000;

    // 单例实例
    private static final LoggerQueue INSTANCE = new LoggerQueue();

    // 用于进程内日志
    private final BlockingQueue<LoggerMessage> blockingQueue = new LinkedBlockingQueue<>(QUEUE_MAX_SIZE);

    // 用于进程外文件日志 - 支持LoggerMessage对象
    private final BlockingQueue<LoggerMessage> fileLogBlockingQueue = new LinkedBlockingQueue<>(QUEUE_MAX_SIZE);

    // 消息序号生成器
    private final AtomicLong sequenceGenerator = new AtomicLong(0);

    /**
     * 私有构造函数
     */
    private LoggerQueue() {
    }

    /**
     * 获取单例实例
     */
    public static LoggerQueue getInstance() {
        return INSTANCE;
    }

    /**
     * 文件日志消息入队 - 支持LoggerMessage对象
     * @param logMessage 日志消息对象
     * @return 是否成功入队
     */
    public boolean pushFileLog(LoggerMessage logMessage) {
        try {
            if (logMessage != null) {
                logMessage.setSequence(sequenceGenerator.incrementAndGet());
                return this.fileLogBlockingQueue.offer(logMessage);
            }
            return false;
        } catch (Exception e) {
            log.error("文件日志入队失败", e);
            return false;
        }
    }

    /**
     * 文件日志消息入队 - 兼容String类型
     * @param logContent 日志内容
     * @return 是否成功入队
     */
    public boolean pushFileLog(String logContent) {
        if (logContent != null && !logContent.trim().isEmpty()) {
            LoggerMessage logMessage = LoggerMessage.builder()
                    .body(logContent)
                    .timestamp(java.time.LocalDateTime.now().format(
                            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .level("INFO")
                    .build();
            return pushFileLog(logMessage);
        }
        return false;
    }

    /**
     * 文件日志消息出队
     * @return 日志消息对象
     */
    public LoggerMessage pollFileLog() {
        try {
            return this.fileLogBlockingQueue.take();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("文件日志出队被中断", e);
            return null;
        }
    }

    /**
     * 非阻塞方式获取文件日志
     * @return 日志消息对象，如果队列为空则返回null
     */
    public LoggerMessage pollFileLogNonBlocking() {
        return this.fileLogBlockingQueue.poll();
    }

    /**
     * 进程内日志消息入队
     * @param logMessage 日志消息对象
     * @return 是否成功入队
     */
    public boolean push(LoggerMessage logMessage) {
        try {
            if (logMessage != null) {
                logMessage.setSequence(sequenceGenerator.incrementAndGet());
                return this.blockingQueue.offer(logMessage);
            }
            return false;
        } catch (Exception e) {
            log.error("进程内日志入队失败", e);
            return false;
        }
    }

    /**
     * 进程内日志消息出队
     * @return 日志消息对象
     */
    public LoggerMessage poll() {
        try {
            return this.blockingQueue.take();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("进程内日志出队被中断", e);
            return null;
        }
    }

    /**
     * 非阻塞方式获取进程内日志
     * @return 日志消息对象，如果队列为空则返回null
     */
    public LoggerMessage pollNonBlocking() {
        return this.blockingQueue.poll();
    }

    /**
     * 获取文件日志队列大小
     */
    public int getFileLogQueueSize() {
        return this.fileLogBlockingQueue.size();
    }

    /**
     * 获取进程内日志队列大小
     */
    public int getProcessLogQueueSize() {
        return this.blockingQueue.size();
    }

    /**
     * 清空所有队列
     */
    public void clearAll() {
        this.fileLogBlockingQueue.clear();
        this.blockingQueue.clear();
        log.info("所有日志队列已清空");
    }

    /**
     * 获取队列状态信息
     */
    public String getQueueStatus() {
        return String.format("文件日志队列: %d/%d, 进程内日志队列: %d/%d",
                getFileLogQueueSize(), QUEUE_MAX_SIZE,
                getProcessLogQueueSize(), QUEUE_MAX_SIZE);
    }
}
