package com.haoys.user.storge.cloud;

import com.haoys.user.enums.system.MimeTypeEnum;
import com.haoys.user.exception.StorageException;
import io.minio.MinioClient;
import io.minio.PutObjectOptions;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * MinIO存储服务
 *
 * <p>优化版本，支持详细的性能监控日志</p>
 *
 * <AUTHOR>
 * @version 2.0.0
 */
@Slf4j
public class MIniOStorageService extends OssStorageService {

    private MinioClient client;

    public MIniOStorageService(OssStorageConfig config) {
        this.config = config;
        //初始化
        init();
        log.info("MinIO存储服务初始化完成 - Endpoint: {}, Bucket: {}",
            config.getEndpoint(), config.getBucketName());
    }

    private void init() {
        try {
            client = new MinioClient(config.getEndpoint(), config.getAccessKeyId(), config.getAccessKeySecret(), false);
            log.debug("MinIO客户端初始化成功");
        } catch (Exception e) {
            log.error("MinIO客户端初始化失败", e);
            throw new StorageException("MinIO客户端初始化失败", e);
        }
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        long startTime = System.currentTimeMillis();
        long fileSize = 0;

        try {
            // 获取文件大小
            try {
                fileSize = inputStream.available();
            } catch (IOException e) {
                log.debug("无法获取输入流大小: {}", e.getMessage());
            }

            log.info("开始上传文件到MinIO - 路径: {}, 预估大小: {}KB", path, fileSize / 1024);

            PutObjectOptions poo = new PutObjectOptions(fileSize, -1);
            poo.setContentType(MimeTypeEnum.getContentType(path));
            client.putObject(config.getBucketName(), path, inputStream, poo);

            long duration = System.currentTimeMillis() - startTime;
            log.info("MinIO文件上传成功 - 路径: {}, 大小: {}KB, 耗时: {}ms",
                path, fileSize / 1024, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("MinIO文件上传失败 - 路径: {}, 大小: {}KB, 耗时: {}ms, 错误: {}",
                path, fileSize / 1024, duration, e.getMessage(), e);
            throw new StorageException("上传文件到MinIO失败: " + e.getMessage(), e);
        }
        return config.getDomain() + "/" + path;
    }

    @Override
    public String upload(byte[] data, String path) {
        try {
            PutObjectOptions poo = new PutObjectOptions(data.length, -1);
            poo.setContentType(MimeTypeEnum.getContentType(path));
            client.putObject(config.getBucketName(), path, new ByteArrayInputStream(data), poo);
        } catch (Exception e) {
            throw new StorageException("上传文件失败，请检查配置信息", e);
        }
        return config.getDomain() + "/" + path;
    }

    @Override
    public void delete(String path) {
        try {
            client.removeObject(config.getBucketName(), path);
        } catch (Exception e) {
            throw new StorageException("删除文件失败", e);
        }
    }

}
