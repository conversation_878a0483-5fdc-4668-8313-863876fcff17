package com.haoys.user.service;

import java.util.List;
import java.util.Map;

/**
 * 数据库表管理服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface DatabaseTableService {
    
    /**
     * 检查表是否存在
     * 
     * @param tableName 表名
     * @return 是否存在
     */
    boolean tableExists(String tableName);
    
    /**
     * 创建表
     * 
     * @param tableName 表名
     * @param createSql 创建SQL
     * @return 是否成功
     */
    boolean createTable(String tableName, String createSql);
    
    /**
     * 删除表
     * 
     * @param tableName 表名
     * @return 是否成功
     */
    boolean dropTable(String tableName);
    
    /**
     * 获取表结构信息
     * 
     * @param tableName 表名
     * @return 表结构信息
     */
    List<Map<String, Object>> getTableStructure(String tableName);
    
    /**
     * 检查并创建业务表
     * 
     * @return 创建结果统计
     */
    Map<String, Object> checkAndCreateBusinessTables();
    
    /**
     * 执行SQL脚本
     * 
     * @param sqlScript SQL脚本内容
     * @return 执行结果
     */
    boolean executeSqlScript(String sqlScript);
    
    /**
     * 批量执行SQL语句
     * 
     * @param sqlStatements SQL语句列表
     * @return 执行结果统计
     */
    Map<String, Object> executeBatchSql(List<String> sqlStatements);
    
    /**
     * 获取数据库信息
     * 
     * @return 数据库信息
     */
    Map<String, Object> getDatabaseInfo();
}
