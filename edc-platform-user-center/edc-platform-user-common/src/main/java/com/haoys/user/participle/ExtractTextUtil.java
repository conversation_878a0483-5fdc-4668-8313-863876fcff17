package com.haoys.user.participle;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class ExtractTextUtil {


    private static String matchStr = "气血失调证,肝郁气滞证,糖尿病,乏力,脾肾两虚证,胃胀,心悸,失眠,脾肾亏虚证,胸闷,月经不调,不寐病,关节痛,尿频,气短,2型糖尿病,蛋白尿,甲状腺结节,泌尿道感染,腰痛,脾胃失调证,呃逆,脂肪肝,子宫肌瘤,湿热内蕴证,颈椎病,肺结节病,心肾不交证,水肿,湿疹,胁痛,高血糖,高脂血症,乳腺增生,腹胀,头晕,颤病,脾肾两亏证,痰瘀阻络证,高尿酸血症,头痛,肺胃热盛证,肝肾亏虚证,眩晕,待查4,呃逆病,糖尿病肾病,支气管扩张,膝关节痛,肝功能异常,肥胖,肢体麻木,瘙痒,胃胀病,糖尿病性周围血管病,胃肠功能紊乱,脾肾不足证,肝郁脾虚证,血糖水平升高,口腔溃疡,肝脾气滞证,气血不足证,肝阳上亢证,代谢综合征,胃脘痛,焦虑状态,肝胃不调证,房颤,肾结石,甲状腺功能低下,肝脾不调证,卵巢囊肿,椎基底动脉供血不足,气虚血瘀证,舌痛,胆囊息肉,胃痛,前列腺肥大,三叉神经痛,冠心病支架术后,门脉高压,湿热下注证,脑梗塞,血小板减少症,胆囊结石,痰瘀互结证,脾大,阴虚内热证,心律失常,心火上炎证,电解质紊乱,肺肾两虚证,听力减退,瘙痒症,肾功能不全,室早,遗尿,动脉狭窄,淋巴结肿大,甲状腺术后,白癜风,脾胃不和证,口干,肥胖病,动脉粥样硬化并高血脂症,月经过少,甲状腺功能亢进症,便秘,抑郁症,乳腺术后";

    public static String spilt(String content) throws IOException {
        Set<String> dataSet = new HashSet<>();
        String[] matchArray = matchStr.split(",");
        for (String target : matchArray) {
            if(content.contains(target)){
                dataSet.add(target);
            }
        }
        /*Iterator<String> iterator = dataSet.iterator();
        StringBuilder stringBuilder=new StringBuilder("");
        while(iterator.hasNext()){
            stringBuilder.append(iterator.next() + ",");
        }*/
        return dataSet.stream().collect(Collectors.joining(","));
    }

    //代赭石（袋）/10g3袋旋覆花（包）/10g1包 枳壳(克)×10克
    //代赭石（袋）/10g3袋旋覆花（包）/10g1包 枳壳
    public static String subStringResource(String text){
        List<String> dataList = new ArrayList<>();
        //数据预处理 替换 袋 包
        text = text.replaceAll("（袋）.*?袋","/克.克").replaceAll("（包）.*?包","/克.克");
        String[] data = text.split("/克.*?克");

        for (String value : data) {
            dataList.add(value);
        }
        return dataList.stream().collect(Collectors.joining(","));
    }


    public static void main(String[] args) throws IOException {
        String content = ExtractTextUtil.spilt("肾炎蛋白尿肝功能异常高血压亚急性甲状腺炎奎尔万甲状腺炎状腺功能异常悸性心动过速肾两虚证血失调证痛");
        System.out.println(content);

        String text = "泽泻/克10克炙黄芪/克30克地龙/克10克苏子/克10克杏仁/克10克茯苓/克15克葛根/克20克瓜蒌/克10克浙贝母/克10克旱莲草/克15克山药/克15克白术/克15克白芍/克30克五味子/克10克麦冬/克15克生地/克15克杷叶/克10克党参/克20克石斛/克30克玉竹/克20克山茱萸/克10克酒黄精/克20克丹皮/克15克陈皮/克10克鱼腥草/克30克";
        /*String[] strings = text.split("/克.*?克");
        for (String value : strings) {
            System.out.println(value);
        }*/
        text = "代赭石（袋）/10g3袋旋覆花（包）/10g1包枳壳/克10克醋柴胡/克10克生黄芪/克30克川牛膝/克10克肉桂/克10克枣仁/克20克远志/克10克石菖蒲/克10克麻黄根/克10克浮小麦/克30克白术/克15克续断/克10克丹皮/克15克白芍/克15克赤芍/克15克泽泻/克10克茯苓/克15克山茱萸/克10克山药/克15克生地/克15克葛根/克15克";
        String stringResource = subStringResource(text);
        System.out.println(stringResource);
    }


}
