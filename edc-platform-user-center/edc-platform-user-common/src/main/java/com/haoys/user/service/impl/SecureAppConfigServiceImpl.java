package com.haoys.user.service.impl;

import cn.hutool.core.util.StrUtil;
import com.haoys.user.mapper.SecureAppConfigMapper;
import com.haoys.user.mapper.SecureAppRequestLogMapper;
import com.haoys.user.model.SecureAppConfig;
import com.haoys.user.service.SecureAppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全应用配置服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class SecureAppConfigServiceImpl implements SecureAppConfigService {
    
    @Autowired
    private SecureAppConfigMapper secureAppConfigMapper;
    
    @Autowired
    private SecureAppRequestLogMapper secureAppRequestLogMapper;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    @Override
    public SecureAppConfig getById(Long id) {
        if (id == null) {
            return null;
        }
        return secureAppConfigMapper.selectByPrimaryKey(id);
    }
    
    @Override
    public SecureAppConfig getByAppIdAndEnvironment(String appId, String environment) {
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(environment)) {
            return null;
        }
        return secureAppConfigMapper.selectByAppIdAndEnvironment(appId, environment);
    }
    
    @Override
    public List<SecureAppConfig> getByEnvironment(String environment) {
        if (StrUtil.isBlank(environment)) {
            environment = getCurrentEnvironment();
        }
        return secureAppConfigMapper.selectByEnvironment(environment);
    }
    
    @Override
    public List<SecureAppConfig> getAllEnabled() {
        return secureAppConfigMapper.selectAllEnabled();
    }
    
    @Override
    public SecureAppConfig validateAppCredentials(String appId, String appSecret, String environment) {
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(appSecret)) {
            log.warn("AppId或AppSecret为空: appId={}, appSecret={}", appId, StrUtil.isBlank(appSecret) ? "空" : "非空");
            return null;
        }
        
        if (StrUtil.isBlank(environment)) {
            environment = getCurrentEnvironment();
        }
        
        SecureAppConfig config = secureAppConfigMapper.validateAppCredentials(appId, appSecret, environment);
        if (config == null) {
            log.warn("应用凭证验证失败: appId={}, environment={}", appId, environment);
            return null;
        }
        
        log.info("应用凭证验证成功: appId={}, appName={}, environment={}", appId, config.getAppName(), environment);
        return config;
    }
    
    @Override
    public SecureAppConfig create(SecureAppConfig config) {
        if (config == null) {
            throw new RuntimeException("应用配置不能为空");
        }
        
        // 设置默认值
        if (config.getStatus() == null) {
            config.setStatus(1);
        }
        if (config.getCodeExpiration() == null) {
            config.setCodeExpiration(120);
        }
        if (config.getAccessTokenExpiration() == null) {
            config.setAccessTokenExpiration(3600);
        }
        if (config.getMaxDailyRequests() == null) {
            config.setMaxDailyRequests(10000);
        }
        if (StrUtil.isBlank(config.getEnvironment())) {
            config.setEnvironment(getCurrentEnvironment());
        }
        
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        
        int result = secureAppConfigMapper.insertSelective(config);
        if (result > 0) {
            log.info("创建应用配置成功: appId={}, appName={}, environment={}", 
                    config.getAppId(), config.getAppName(), config.getEnvironment());
            return config;
        } else {
            throw new RuntimeException("创建应用配置失败");
        }
    }
    
    @Override
    public SecureAppConfig update(SecureAppConfig config) {
        if (config == null || config.getId() == null) {
            throw new RuntimeException("应用配置ID不能为空");
        }
        
        config.setUpdateTime(LocalDateTime.now());
        
        int result = secureAppConfigMapper.updateByPrimaryKeySelective(config);
        if (result > 0) {
            log.info("更新应用配置成功: id={}, appId={}", config.getId(), config.getAppId());
            return secureAppConfigMapper.selectByPrimaryKey(config.getId());
        } else {
            throw new RuntimeException("更新应用配置失败");
        }
    }
    
    @Override
    public boolean delete(Long id) {
        if (id == null) {
            return false;
        }
        
        SecureAppConfig config = secureAppConfigMapper.selectByPrimaryKey(id);
        if (config == null) {
            return false;
        }
        
        int result = secureAppConfigMapper.deleteByPrimaryKey(id);
        if (result > 0) {
            log.info("删除应用配置成功: id={}, appId={}", id, config.getAppId());
            return true;
        }
        return false;
    }
    
    @Override
    public boolean deleteByAppIdAndEnvironment(String appId, String environment) {
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(environment)) {
            return false;
        }
        
        int result = secureAppConfigMapper.deleteByAppIdAndEnvironment(appId, environment);
        if (result > 0) {
            log.info("删除应用配置成功: appId={}, environment={}", appId, environment);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean checkDailyRequestLimit(String appId, String environment) {
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(environment)) {
            return false;
        }
        
        SecureAppConfig config = getByAppIdAndEnvironment(appId, environment);
        if (config == null || config.getMaxDailyRequests() == null) {
            return true; // 如果没有配置限制，则允许
        }
        
        int todayRequests = secureAppRequestLogMapper.countTodayRequests(appId, environment);
        boolean withinLimit = todayRequests < config.getMaxDailyRequests();
        
        if (!withinLimit) {
            log.warn("应用今日请求次数超限: appId={}, environment={}, todayRequests={}, maxDailyRequests={}", 
                    appId, environment, todayRequests, config.getMaxDailyRequests());
        }
        
        return withinLimit;
    }
    
    @Override
    public String getCurrentEnvironment() {
        // 根据Spring的active profile确定当前环境
        if (StrUtil.isBlank(activeProfile)) {
            return "dev";
        }
        
        // 处理多个profile的情况，取第一个
        String[] profiles = activeProfile.split(",");
        String currentEnv = profiles[0].trim();
        
        // 映射一些特殊的profile名称
        switch (currentEnv) {
            case "local":
                return "local";
            case "development":
            case "dev":
                return "dev";
            case "testing":
            case "test":
                return "test";
            case "staging":
            case "feature":
                return "feature";
            case "production":
            case "prod":
                return "prod";
            default:
                return currentEnv;
        }
    }
}
