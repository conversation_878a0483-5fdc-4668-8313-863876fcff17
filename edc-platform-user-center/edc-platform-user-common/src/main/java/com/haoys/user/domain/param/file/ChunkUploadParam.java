package com.haoys.user.domain.param.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分片上传参数
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 */
@Data
@ApiModel(description = "分片上传参数")
public class ChunkUploadParam {

    @ApiModelProperty(value = "文件MD5哈希值", required = true, example = "d41d8cd98f00b204e9800998ecf8427e")
    @NotBlank(message = "文件哈希值不能为空")
    private String fileHash;

    @ApiModelProperty(value = "文件名称", required = true, example = "document.pdf")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    @ApiModelProperty(value = "文件大小（字节）", required = true, example = "52428800")
    @NotNull(message = "文件大小不能为空")
    @Min(value = 1, message = "文件大小必须大于0")
    private Long fileSize;

    @ApiModelProperty(value = "分片索引（从0开始）", required = true, example = "0")
    @NotNull(message = "分片索引不能为空")
    @Min(value = 0, message = "分片索引不能小于0")
    private Integer chunkIndex;

    @ApiModelProperty(value = "分片大小（字节）", required = true, example = "2097152")
    @NotNull(message = "分片大小不能为空")
    @Min(value = 1, message = "分片大小必须大于0")
    private Long chunkSize;

    @ApiModelProperty(value = "总分片数", required = true, example = "25")
    @NotNull(message = "总分片数不能为空")
    @Min(value = 1, message = "总分片数必须大于0")
    private Integer totalChunks;

    @ApiModelProperty(value = "分片MD5哈希值", required = true, example = "5d41402abc4b2a76b9719d911017c592")
    @NotBlank(message = "分片哈希值不能为空")
    private String chunkHash;

    @ApiModelProperty(value = "项目ID", example = "123")
    private String projectId;

    @ApiModelProperty(value = "文件夹名称", example = "documents")
    private String folderName;

    @ApiModelProperty(value = "文件类型", example = "application/pdf")
    private String contentType;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "扩展信息（JSON格式）", example = "{\"description\":\"项目文档\"}")
    private String extendInfo;

    @ApiModelProperty(value = "是否启用压缩", example = "false")
    private Boolean enableCompression = false;

    @ApiModelProperty(value = "存储类型（1=本地，2=七牛云，3=阿里云）", example = "1")
    private Integer storageType;
}
