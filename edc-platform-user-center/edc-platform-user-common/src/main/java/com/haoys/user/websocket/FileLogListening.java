package com.haoys.user.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 文件日志监听服务
 * <p>实时监听日志文件变化，通过WebSocket推送给前端显示</p>
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Slf4j
@Service
public class FileLogListening {

    private volatile long lastTimeFileSize = 0;  // 上次文件大小
    private ScheduledExecutorService executorService;
    private RandomAccessFile randomFile;
    private volatile boolean isRunning = false;

    @Autowired
    private Environment environment;

    @Value("${logging.file.name:logs/application.log}")
    private String logFilePath;

    @Value("${websocket.log.scan-interval:1}")
    private int scanIntervalSeconds;

    @Value("${websocket.log.max-lines:1000}")
    private int maxLines;

    @Value("${websocket.log.enabled:false}")
    private boolean logEnabled;

    @Value("${websocket.log.online-output-enabled:true}")
    private boolean onlineOutputEnabled;

    @Value("${websocket.log.print-enabled:true}")
    private boolean printEnabled;

    /**
     * 启动日志文件监听
     */
    @PostConstruct
    public void start() {
        // 检查是否启用在线日志输出功能
        if (!onlineOutputEnabled) {
            if (logEnabled) {
                log.info("在线日志输出功能已禁用，跳过日志文件监听服务启动");
            }
            return;
        }

        try {
            initLogFile();
            startMonitoring();
            if (logEnabled) {
                log.info("日志文件监听服务启动成功，监听文件: {}", logFilePath);
            }
        } catch (Exception e) {
            if (logEnabled) {
                log.error("启动日志文件监听服务失败", e);
            }
        }
    }

    /**
     * 停止日志文件监听
     */
    @PreDestroy
    public void stop() {
        isRunning = false;
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (randomFile != null) {
            try {
                randomFile.close();
            } catch (IOException e) {
                log.error("关闭日志文件失败", e);
            }
        }
        if (logEnabled) {
            log.info("日志文件监听服务已停止");
        }
    }

    /**
     * 初始化日志文件
     */
    private void initLogFile() throws IOException {
        Path logPath = Paths.get(logFilePath);

        // 确保日志文件存在
        if (!Files.exists(logPath)) {
            Files.createDirectories(logPath.getParent());
            Files.createFile(logPath);
            if (logEnabled) {
                log.info("创建日志文件: {}", logFilePath);
            }
        }

        File logFile = logPath.toFile();
        randomFile = new RandomAccessFile(logFile, "r");
        lastTimeFileSize = logFile.length();

        if (logEnabled) {
            log.info("初始化日志文件成功，当前文件大小: {} bytes", lastTimeFileSize);
        }
    }

    /**
     * 开始监控日志文件
     */
    private void startMonitoring() {
        executorService = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "log-file-monitor");
            thread.setDaemon(true);
            return thread;
        });

        isRunning = true;
        executorService.scheduleWithFixedDelay(this::readNewLogLines,
                0, scanIntervalSeconds, TimeUnit.SECONDS);
    }

    /**
     * 读取新的日志行
     */
    private void readNewLogLines() {
        if (!isRunning) {
            return;
        }

        try {
            long currentFileSize = randomFile.length();

            // 文件被截断或重新创建
            if (currentFileSize < lastTimeFileSize) {
                if (logEnabled) {
                    log.info("检测到日志文件被重置，重新开始读取");
                }
                lastTimeFileSize = 0;
                randomFile.seek(0);
            }

            // 文件有新内容
            if (currentFileSize > lastTimeFileSize) {
                randomFile.seek(lastTimeFileSize);

                String line;
                int lineCount = 0;
                while ((line = randomFile.readLine()) != null && lineCount < maxLines) {
                    // 处理编码问题
                    String logLine = new String(line.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);

                    // 创建日志消息对象
                    LoggerMessage logMessage = LoggerMessage.builder()
                            .content(logLine)
                            .timestamp(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                            .level(extractLogLevel(logLine))
                            .build();

                    // 推送到队列（仅在启用打印功能时）
                    if (printEnabled) {
                        LoggerQueue.getInstance().pushFileLog(logMessage);
                    }
                    lineCount++;
                }

                lastTimeFileSize = randomFile.getFilePointer();

                if (lineCount > 0 && logEnabled) {
                    log.debug("读取到 {} 行新日志", lineCount);
                }
            }
        } catch (IOException e) {
            if (logEnabled) {
                log.error("读取日志文件时发生错误", e);
            }
            // 尝试重新初始化文件
            try {
                initLogFile();
            } catch (IOException initException) {
                if (logEnabled) {
                    log.error("重新初始化日志文件失败", initException);
                }
            }
        }
    }

    /**
     * 提取日志级别
     */
    private String extractLogLevel(String logLine) {
        if (logLine.contains("ERROR")) return "ERROR";
        if (logLine.contains("WARN")) return "WARN";
        if (logLine.contains("INFO")) return "INFO";
        if (logLine.contains("DEBUG")) return "DEBUG";
        if (logLine.contains("TRACE")) return "TRACE";
        return "INFO";
    }
}