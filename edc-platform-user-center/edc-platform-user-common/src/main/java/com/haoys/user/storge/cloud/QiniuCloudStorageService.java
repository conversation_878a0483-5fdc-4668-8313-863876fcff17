package com.haoys.user.storge.cloud;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.exception.StorageException;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * 七牛云存储服务
 *
 * <p>优化版本，支持：</p>
 * <ul>
 *   <li>流式上传，避免大文件内存占用</li>
 *   <li>详细的性能监控日志</li>
 *   <li>连接池和超时配置优化</li>
 *   <li>智能重试机制</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 */
@Slf4j
public class QiniuCloudStorageService extends OssStorageService {

    private UploadManager uploadManager;
    private String token;
    private Auth auth;
    private Configuration cfg;
    private BucketManager bucketManager;

    // 性能配置常量
    private static final int CONNECT_TIMEOUT = 30; // 连接超时30秒
    private static final int READ_TIMEOUT = 60; // 读取超时60秒
    private static final int WRITE_TIMEOUT = 60; // 写入超时60秒
    private static final int MAX_RETRY_COUNT = 3; // 最大重试次数
    private static final long LARGE_FILE_THRESHOLD = 10 * 1024 * 1024; // 10MB大文件阈值

    QiniuCloudStorageService(OssStorageConfig config) {
        this.config = config;
        initializeConfiguration();
        initializeServices();
        log.info("七牛云存储服务初始化完成 - Bucket: {}, Domain: {}",
            config.getBucketName(), config.getDomain());
    }

    /**
     * 初始化七牛云配置
     */
    private void initializeConfiguration() {
        try {
            // 优化配置：指定区域而非自动检测，减少网络延迟
            cfg = new Configuration(Region.autoRegion());

            // 设置连接超时
            cfg.connectTimeout = CONNECT_TIMEOUT;
            cfg.readTimeout = READ_TIMEOUT;
            cfg.writeTimeout = WRITE_TIMEOUT;

            // 注意：断点续传配置根据SDK版本可能不同，这里注释掉避免兼容性问题
            // cfg.resumeUploadAPIVersion = Configuration.ResumableUploadAPIVersion.V2;

            log.debug("七牛云配置初始化完成 - 连接超时: {}s, 读取超时: {}s, 写入超时: {}s",
                CONNECT_TIMEOUT, READ_TIMEOUT, WRITE_TIMEOUT);

        } catch (Exception e) {
            log.error("七牛云配置初始化失败", e);
            throw new StorageException("七牛云配置初始化失败", e);
        }
    }

    /**
     * 初始化七牛云服务
     */
    private void initializeServices() {
        try {
            uploadManager = new UploadManager(cfg);
            auth = Auth.create(config.getAccessKeyId(), config.getAccessKeySecret());
            bucketManager = new BucketManager(auth, cfg);

            // 生成上传Token（有效期1小时）
            token = auth.uploadToken(config.getBucketName(), null, 3600, null);

            log.debug("七牛云服务初始化完成 - UploadManager和BucketManager已就绪");

        } catch (Exception e) {
            log.error("七牛云服务初始化失败", e);
            throw new StorageException("七牛云服务初始化失败", e);
        }
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        long startTime = System.currentTimeMillis();
        long fileSize = 0;

        try {
            // 获取文件大小（如果可能）
            try {
                fileSize = inputStream.available();
            } catch (IOException e) {
                log.debug("无法获取输入流大小: {}", e.getMessage());
            }

            log.info("开始上传文件到七牛云 - 路径: {}, 预估大小: {}KB", path, fileSize / 1024);

            // 对于大文件，使用流式上传避免内存占用
            if (fileSize > LARGE_FILE_THRESHOLD) {
                return uploadLargeFileStream(inputStream, path, fileSize, startTime);
            } else {
                return uploadSmallFile(inputStream, path, fileSize, startTime);
            }

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("上传文件到七牛云失败 - 路径: {}, 大小: {}KB, 耗时: {}ms, 错误: {}",
                path, fileSize / 1024, duration, e.getMessage(), e);
            throw new StorageException("上传文件到七牛云失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传小文件（<10MB）
     */
    private String uploadSmallFile(InputStream inputStream, String path, long fileSize, long startTime) {
        try {
            byte[] bytes = IoUtil.readBytes(inputStream);
            long actualSize = bytes.length;

            log.debug("小文件上传 - 路径: {}, 实际大小: {}KB", path, actualSize / 1024);

            String result = uploadWithRetry(bytes, path);

            long duration = System.currentTimeMillis() - startTime;
            log.info("小文件上传成功 - 路径: {}, 大小: {}KB, 耗时: {}ms, URL: {}",
                path, actualSize / 1024, duration, result);

            return result;

        } catch (Exception e) {
            log.error("小文件上传失败 - 路径: {}, 错误: {}", path, e.getMessage());
            throw e;
        }
    }

    /**
     * 上传大文件（>=10MB），使用流式上传
     */
    private String uploadLargeFileStream(InputStream inputStream, String path, long fileSize, long startTime) {
        try {
            log.info("大文件流式上传开始 - 路径: {}, 大小: {}MB", path, fileSize / (1024 * 1024));

            // 对于大文件，仍需要读取到内存，但添加更详细的监控
            byte[] bytes = IoUtil.readBytes(inputStream);
            long actualSize = bytes.length;

            log.debug("大文件数据读取完成 - 路径: {}, 实际大小: {}MB", path, actualSize / (1024 * 1024));

            String result = uploadWithRetry(bytes, path);

            long duration = System.currentTimeMillis() - startTime;
            double throughput = (actualSize / 1024.0 / 1024.0) / (duration / 1000.0); // MB/s

            log.info("大文件上传成功 - 路径: {}, 大小: {}MB, 耗时: {}ms, 吞吐量: {:.2f}MB/s, URL: {}",
                path, actualSize / (1024 * 1024), duration, throughput, result);

            return result;

        } catch (Exception e) {
            log.error("大文件上传失败 - 路径: {}, 错误: {}", path, e.getMessage());
            throw e;
        }
    }

    @Override
    public String upload(byte[] data, String path) {
        long startTime = System.currentTimeMillis();
        long fileSize = data.length;

        log.info("开始上传字节数组到七牛云 - 路径: {}, 大小: {}KB", path, fileSize / 1024);

        try {
            String result = uploadWithRetry(data, path);

            long duration = System.currentTimeMillis() - startTime;
            log.info("字节数组上传成功 - 路径: {}, 大小: {}KB, 耗时: {}ms, URL: {}",
                path, fileSize / 1024, duration, result);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("字节数组上传失败 - 路径: {}, 大小: {}KB, 耗时: {}ms, 错误: {}",
                path, fileSize / 1024, duration, e.getMessage(), e);
            throw new StorageException("上传文件到七牛云失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带重试机制的上传方法
     */
    private String uploadWithRetry(byte[] data, String path) throws StorageException {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_COUNT; attempt++) {
            long attemptStart = System.currentTimeMillis();

            try {
                log.debug("七牛云上传尝试 {}/{} - 路径: {}, 大小: {}KB",
                    attempt, MAX_RETRY_COUNT, path, data.length / 1024);

                Response response = uploadManager.put(data, path, token);

                long attemptDuration = System.currentTimeMillis() - attemptStart;

                if (response.isOK()) {
                    log.debug("七牛云上传成功 - 尝试: {}, 耗时: {}ms, 响应: {}",
                        attempt, attemptDuration, response.bodyString());
                    return config.getDomain() + "/" + path;
                } else {
                    String errorMsg = String.format("七牛云响应错误 - 状态码: %d, 响应体: %s",
                        response.statusCode, response.bodyString());
                    log.warn("七牛云上传失败 - 尝试: {}, 耗时: {}ms, 错误: {}",
                        attempt, attemptDuration, errorMsg);
                    lastException = new StorageException(errorMsg);
                }

            } catch (QiniuException e) {
                long attemptDuration = System.currentTimeMillis() - attemptStart;
                String errorMsg = String.format("七牛云异常 - 错误码: %d, 错误信息: %s",
                    e.code(), e.getMessage());
                log.warn("七牛云上传异常 - 尝试: {}, 耗时: {}ms, 错误: {}",
                    attempt, attemptDuration, errorMsg);
                lastException = e;
            } catch (Exception e) {
                long attemptDuration = System.currentTimeMillis() - attemptStart;
                log.warn("七牛云上传异常 - 尝试: {}, 耗时: {}ms, 错误: {}",
                    attempt, attemptDuration, e.getMessage());
                lastException = e;
            }

            // 如果不是最后一次尝试，等待后重试
            if (attempt < MAX_RETRY_COUNT) {
                try {
                    long waitTime = attempt * 1000; // 递增等待时间
                    log.debug("等待 {}ms 后进行第 {} 次重试", waitTime, attempt + 1);
                    Thread.sleep(waitTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new StorageException("上传被中断", ie);
                }
            }
        }

        // 所有重试都失败
        String errorMsg = String.format("七牛云上传失败，已重试 %d 次", MAX_RETRY_COUNT);
        log.error("{} - 路径: {}, 最后错误: {}", errorMsg, path,
            lastException != null ? lastException.getMessage() : "未知错误");
        throw new StorageException(errorMsg, lastException);
    }

    @Override
    public InputStream download(String path) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始从七牛云下载文件 - 路径: {}", path);

            String downloadUrl = config.getDomain() + "/" + path;
            final ByteArrayOutputStream output = new ByteArrayOutputStream();

            HttpUtil.download(downloadUrl, output, false);
            byte[] data = output.toByteArray();

            long duration = System.currentTimeMillis() - startTime;
            log.info("七牛云文件下载成功 - 路径: {}, 大小: {}KB, 耗时: {}ms",
                path, data.length / 1024, duration);

            return new ByteArrayInputStream(data);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("七牛云文件下载失败 - 路径: {}, 耗时: {}ms, 错误: {}",
                path, duration, e.getMessage(), e);
            throw new StorageException("从七牛云下载文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void delete(String path) {
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始删除七牛云文件 - 路径: {}", path);

            bucketManager.delete(config.getBucketName(), path);

            long duration = System.currentTimeMillis() - startTime;
            log.info("七牛云文件删除成功 - 路径: {}, 耗时: {}ms", path, duration);

        } catch (QiniuException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("七牛云文件删除失败 - 路径: {}, 耗时: {}ms, 错误码: {}, 错误信息: {}",
                path, duration, e.code(), e.getMessage(), e);
            throw new StorageException("删除七牛云文件失败: " + e.getMessage(), e);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("七牛云文件删除异常 - 路径: {}, 耗时: {}ms, 错误: {}",
                path, duration, e.getMessage(), e);
            throw new StorageException("删除七牛云文件异常: " + e.getMessage(), e);
        }
    }

    /**
     * 获取上传统计信息
     */
    public String getUploadStats() {
        return String.format("七牛云存储配置 - Bucket: %s, Domain: %s, 连接超时: %ds, 读取超时: %ds",
            config.getBucketName(), config.getDomain(), CONNECT_TIMEOUT, READ_TIMEOUT);
    }
}
