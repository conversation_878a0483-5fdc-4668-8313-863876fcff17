//package com.haoys.mis.service;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.connection.stream.*;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.stream.StreamListener;
//import org.springframework.data.redis.stream.StreamReceiver;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.time.Duration;
//import java.util.HashMap;
//import java.util.Map;
//
//@Service
//public class MessageQueueService {
//
//    @Autowired
//    private RedisTemplate<String, String> redisTemplate;
//
//    private static final String STREAM_KEY = "messageStream";
//    private static final String RETRY_STREAM_KEY = "retryStream";
//    private static final String CONSUMER_GROUP = "messageConsumerGroup";
//
//    public void sendMessage(String message) {
//        Map<String, Object> fields = new HashMap<>();
//        fields.put("message", message);
//        fields.put("priority", "normal");
//        fields.put("timestamp", System.currentTimeMillis());
//
//        RecordId recordId = redisTemplate.opsForStream().add(ObjectRecord.create(STREAM_KEY, fields));
//        System.out.println("Message sent: " + recordId);
//    }
//
//    @PostConstruct
//    public void init() {
//        createConsumerGroup();
//        consumeMessages();
//        consumeRetryMessages();
//    }
//
//    private void createConsumerGroup() {
//        try {
//            redisTemplate.opsForStream().createGroup(STREAM_KEY, ReadOffset.latest(), CONSUMER_GROUP);
//            redisTemplate.opsForStream().createGroup(RETRY_STREAM_KEY, ReadOffset.latest(), CONSUMER_GROUP);
//        } catch (Exception e) {
//            System.out.println("Consumer group already exists, skipping creation");
//        }
//    }
//
//    public void consumeMessages() {
//        StreamReceiver<String, MapRecord<String, String, String>> receiver = redisTemplate.opsForStream().consumeAutoAck("myStream");
//        receiver.receive().subscribe(record -> {
//            RecordId recordId = record.getId();
//            Map<String, String> fields = record.getValue().toMap();
//            System.out.println("接收到的消息: " + fields);
//        });
//    }
//
//    private void consumeMessages() {
//
//        StreamListener<String, ObjectRecord<String, Map<String, Object>>>) record -> {
//            System.out.println("Received message: " + record.getValue().get("message"));
//            try {
//                // Process the message
//            } catch (Exception e) {
//                // If processing fails, re-add the message to the retry stream with a delay
//                sendRetryMessage((String) record.getValue().get("message"), Duration.ofSeconds(10));
//            }
//        }
//
//
//        redisTemplate.opsForStream().acknowledge(STREAM_KEY, Consumer.from(CONSUMER_GROUP, "consumer1"), ();
//    }
//
//    private void consumeRetryMessages() {
//        redisTemplate.opsForStream().consumeAutoAck(RETRY_STREAM_KEY, Consumer.from(CONSUMER_GROUP, "consumer1"), (StreamListener<String, ObjectRecord<String, Map<String, Object>>>) record -> {
//            System.out.println("Received retry message: " + record.getValue().get("message"));
//            try {
//                // Process the message
//            } catch (Exception e) {
//                // If processing fails again, re-add the message to the retry stream with a longer delay
//                sendRetryMessage((String) record.getValue().get("message"), Duration.ofSeconds(30));
//            }
//        });
//    }
//
//    public void sendRetryMessage(String message, Duration delay) {
//        Map<String, Object> fields = new HashMap<>();
//        fields.put("message", message);
//        fields.put("priority", "normal");
//        fields.put("timestamp", System.currentTimeMillis() + delay.toMillis());
//
//        redisTemplate.opsForStream().add(ObjectRecord.create(RETRY_STREAM_KEY, fields));
//    }
//}
//
