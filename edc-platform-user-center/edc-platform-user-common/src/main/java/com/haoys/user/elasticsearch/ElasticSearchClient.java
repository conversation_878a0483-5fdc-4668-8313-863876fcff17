//package com.haoys.mis.elasticsearch;
//
//import com.haoys.mis.config.ElasticSearchClientConfig;
//import com.haoys.mis.common.constants.DefineConstant;
//import com.haoys.mis.common.util.StringUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.http.HttpHost;
//import org.apache.http.auth.AuthScope;
//import org.apache.http.auth.UsernamePasswordCredentials;
//import org.apache.http.client.CredentialsProvider;
//import org.apache.http.client.config.RequestConfig;
//import org.apache.http.impl.client.BasicCredentialsProvider;
//import org.elasticsearch.client.RestClient;
//import org.elasticsearch.client.RestClientBuilder;
//import org.elasticsearch.client.RestHighLevelClient;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//
//@Slf4j
//public class ElasticSearchClient {
//
//    public static String HOSTS;
//
//    public static int CONNECTTIMEOUT;
//
//    public static int SOCKETTIMEOUT;
//
//    public static int MAXRETRYTIMEOUT;
//
//    public static int CONNREQUESTTIMEOUT;
//
//    public static String SEARCH_REQUEST_TYPE;
//
//    public static String INDEXNAME;
//
//    public static String USERNAME;
//
//    public static String PASSWORD;
//
//    private static RestHighLevelClient restHighLevelClient = null;
//
//    public static void setConfigInfo(ElasticSearchClientConfig elasticSearchClientConfig) {
//        ElasticSearchClient.HOSTS = elasticSearchClientConfig.getHosts();
//        ElasticSearchClient.CONNECTTIMEOUT = elasticSearchClientConfig.getConnectTimeout();
//        ElasticSearchClient.SOCKETTIMEOUT = elasticSearchClientConfig.getSocketTimeout();
//        ElasticSearchClient.MAXRETRYTIMEOUT = elasticSearchClientConfig.getMaxRetryTimeout();
//        ElasticSearchClient.CONNREQUESTTIMEOUT = elasticSearchClientConfig.getConnRequestTimeOut();
//        ElasticSearchClient.INDEXNAME = elasticSearchClientConfig.getIndexName();
//        ElasticSearchClient.SEARCH_REQUEST_TYPE = elasticSearchClientConfig.getSearchRequestType();
//        ElasticSearchClient.USERNAME = elasticSearchClientConfig.getUserName();
//        ElasticSearchClient.PASSWORD = elasticSearchClientConfig.getPassword();
//    }
//
//    public static RestHighLevelClient getInstance(){
//        if (restHighLevelClient == null){
//            synchronized (RestHighLevelClient.class){
//                restHighLevelClient = createClient();
//            }
//        }
//        return restHighLevelClient;
//    }
//
//    private static RestHighLevelClient createClient(){
//        List<String> sockets = Arrays.asList(HOSTS.split(DefineConstant.COMMAN_SIGN));
//        List<HttpHost> httpHosts = new ArrayList<>();
//        for (String socket : sockets){
//            httpHosts.add(new HttpHost(socket.split(DefineConstant.COLON_SIGN)[0], Integer.valueOf(socket.split(DefineConstant.COLON_SIGN)[1]),"http"));
//        }
//
//        if(StringUtils.isNotEmpty(USERNAME)){
//            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
//            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(USERNAME, PASSWORD));
//        }
//
//        RestClientBuilder builder = RestClient.builder(httpHosts.toArray(new HttpHost[0])).setMaxRetryTimeoutMillis(MAXRETRYTIMEOUT);
//        builder.setRequestConfigCallback((RequestConfig.Builder requestConfigBuilder) -> {
//            requestConfigBuilder.setConnectTimeout(CONNECTTIMEOUT);
//            requestConfigBuilder.setConnectionRequestTimeout(CONNREQUESTTIMEOUT);
//            requestConfigBuilder.setSocketTimeout(SOCKETTIMEOUT);
//            return requestConfigBuilder;
//        });
//
//        restHighLevelClient = new RestHighLevelClient(builder);
//        return restHighLevelClient;
//    }
//}
