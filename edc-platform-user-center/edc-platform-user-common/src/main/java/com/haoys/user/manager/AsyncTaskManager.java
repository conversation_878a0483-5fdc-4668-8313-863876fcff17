package com.haoys.user.manager;

import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.common.util.Threads;


import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 异步任务管理器
 */
public class AsyncTaskManager {
    /**
     * 操作延迟100毫秒
     */
    private final int OPERATE_DELAY_TIME = 1000;

    /**
     * 异步操作任务调度线程池
     */
    private ScheduledExecutorService executor = SpringUtils.getBean("scheduledExecutorService");

    /**
     * 单例模式
     */
    private AsyncTaskManager() {
    }

    private static AsyncTaskManager asyncTaskManager = new AsyncTaskManager();

    public static AsyncTaskManager ownerTask() {
        return asyncTaskManager;
    }

    /**
     * 执行任务
     *
     * @param task 任务
     */
    public void execute(TimerTask task) {
        executor.schedule(task, OPERATE_DELAY_TIME, TimeUnit.MILLISECONDS);
    }

    public void execute(TimerTask task, int delayTime) {
        executor.schedule(task, delayTime, TimeUnit.MILLISECONDS);
    }

    /**
     * 停止任务线程池
     */
    public void shutdown() {
        Threads.shutdownAndAwaitTermination(executor);
    }
}
