package com.haoys.user.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 安全Token响应对象
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "安全Token响应对象")
public class SecureTokenVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 生成Code响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "生成Code响应")
    public static class CodeResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "生成的code", example = "code_abc123")
        private String code;
        
        @ApiModelProperty(value = "刷新码", example = "refresh_xyz789")
        private String refreshCode;
        
        @ApiModelProperty(value = "过期时间")
        private LocalDateTime expireTime;
        
        @ApiModelProperty(value = "有效期（秒）", example = "120")
        private Long expiresIn;
    }
    
    /**
     * AccessToken响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "AccessToken响应")
    public static class AccessTokenResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "访问令牌", example = "access_abc123")
        private String accessToken;
        
        @ApiModelProperty(value = "过期时间")
        private LocalDateTime expireTime;
        
        @ApiModelProperty(value = "有效期（秒）", example = "3600")
        private Long expiresIn;
        
        @ApiModelProperty(value = "关联的用户ID", example = "user123")
        private String userId;
    }
    
    /**
     * Token验证响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "Token验证响应")
    public static class ValidateResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "是否有效", example = "true")
        private Boolean valid;
        
        @ApiModelProperty(value = "关联的用户ID", example = "user123")
        private String userId;
        
        @ApiModelProperty(value = "剩余有效期（秒）", example = "1800")
        private Long remainingTime;
        
        @ApiModelProperty(value = "扩展信息", example = "extra info")
        private String extraInfo;
    }
}
