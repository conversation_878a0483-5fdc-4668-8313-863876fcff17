package com.haoys.user.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 日志消息实体类
 *
 * <p>用于封装日志信息，通过WebSocket传输到前端</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoggerMessage {

    private String body;
    private String timestamp;
    private String threadName;
    private String className;
    private String level;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 日志序号
     */
    private Long sequence;

    // 兼容旧版本的构造函数
    public LoggerMessage(String body, String timestamp, String threadName, String className, String level) {
        this.body = body;
        this.timestamp = timestamp;
        this.threadName = threadName;
        this.className = className;
        this.level = level;
    }

    // 简化构造函数
    public LoggerMessage(String body, String timestamp) {
        this.body = body;
        this.timestamp = timestamp;
        this.level = "INFO";
    }

    /**
     * 获取格式化的日志信息
     */
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder();
        if (timestamp != null) {
            sb.append("[").append(timestamp).append("] ");
        }
        if (level != null) {
            sb.append(level).append(" ");
        }
        if (threadName != null) {
            sb.append("[").append(threadName).append("] ");
        }
        if (className != null) {
            sb.append(className).append(" - ");
        }
        if (body != null) {
            sb.append(body);
        }
        return sb.toString();
    }

    /**
     * 判断是否为错误级别日志
     */
    public boolean isError() {
        return "ERROR".equalsIgnoreCase(level);
    }

    /**
     * 判断是否为警告级别日志
     */
    public boolean isWarn() {
        return "WARN".equalsIgnoreCase(level);
    }
}
