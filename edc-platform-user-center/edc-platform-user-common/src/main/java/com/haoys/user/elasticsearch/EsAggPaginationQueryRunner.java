//package com.haoys.mis.elasticsearch;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.lucene.search.join.ScoreMode;
//import org.elasticsearch.action.search.SearchRequest;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.client.RequestOptions;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.NestedQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.script.Script;
//import org.elasticsearch.script.ScriptType;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.SearchHits;
//import org.elasticsearch.search.aggregations.AggregationBuilders;
//import org.elasticsearch.search.aggregations.bucket.terms.Terms;
//import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
//import org.elasticsearch.search.aggregations.metrics.cardinality.Cardinality;
//import org.elasticsearch.search.aggregations.pipeline.bucketsort.BucketSortPipelineAggregationBuilder;
//import org.elasticsearch.search.builder.SearchSourceBuilder;
//import org.elasticsearch.search.sort.FieldSortBuilder;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * es分页查询执行器
// */
//@Slf4j
//public class EsAggPaginationQueryRunner {
//
//    private final RestHighLevelClient restHighLevelClient;
//    /**
//     * searchRequest对象
//     */
//    private final SearchRequest searchRequest = new SearchRequest();
//    /**
//     * 不需要返回数据,size设为0
//     */
//    private final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().size(0);
//    /**
//     * 分组统计的字段
//     */
//    private String[] groupByFields;
//    /**
//     * 查询时的script
//     */
//    private Script queryScript;
//    /**
//     * 分隔符
//     */
//    private final String spiltSymbol = "@#@#@";
//
//    private EsAggPaginationQueryRunner(RestHighLevelClient restHighLevelClient) {
//        this.restHighLevelClient = restHighLevelClient;
//    }
//
//    public EsAggPaginationQueryRunner build(String esIndexName, BoolQueryBuilder boolQueryBuilder, String... groupByFields) {
//        //添加查询条件
//        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("variable_info", boolQueryBuilder, ScoreMode.Max);
//        searchSourceBuilder.query(nestedQuery);
//
//        //指定索引
//        searchRequest.indices(esIndexName);
//        //指定分组的字段
//        this.groupByFields = groupByFields;
//        //查询script初始化
//        queryScript = initScript();
//        return this;
//    }
//
//    public static EsAggPaginationQueryRunner client(RestHighLevelClient restHighLevelClient) {
//        //建立一个新对象
//        return new EsAggPaginationQueryRunner(restHighLevelClient);
//    }
//
//
//    public Script initScript() {
//        //定义script
//        StringBuilder scriptBuilder = new StringBuilder();
//        for (int i = 0; i < groupByFields.length; i++) {
//            if (i != 0) {
//                scriptBuilder.append("+'" + spiltSymbol + "'+");
//            }
//            scriptBuilder.append(String.format("doc['%s'].value", groupByFields[i]));
//        }
//        return new Script(ScriptType.INLINE, "painless", scriptBuilder.toString(), new HashMap<>(0));
//    }
//
//    /**
//     * 进行聚合
//     */
//    public ResultTotal getResultTotal() throws Exception {
//        //定义script
//        StringBuilder scriptBuilder = new StringBuilder();
//        for (int i = 0; i < groupByFields.length; i++) {
//            if (i != 0) {
//                scriptBuilder.append(" +' '+");
//            }
//            scriptBuilder.append(String.format("doc['%s'].value", groupByFields[i]));
//        }
//
//        Script getTotalScript = new Script(ScriptType.INLINE, "painless", scriptBuilder.toString(), new HashMap<>(0));
//        SearchSourceBuilder distinctByFieldAgg = searchSourceBuilder.aggregation(AggregationBuilders.cardinality("distinct_by_field").script(getTotalScript));
//        //指定source
//        searchRequest.source(distinctByFieldAgg);
//        //执行查询
//        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
//        SearchHits searchHits = searchResponse.getHits();
//        SearchHit[] searchHitsHits = searchHits.getHits();
//        Map<String, Object> sourceAsMap = null;
//        for (SearchHit searchHitsHit : searchHitsHits) {
//            sourceAsMap = searchHitsHit.getSourceAsMap();
//        }
//        //获取出查询结果
//        long groupTotal = ((Cardinality) searchResponse.getAggregations().get("distinct_by_field")).getValue();
//        long totalHits = searchResponse.getHits().totalHits;
//        return new ResultTotal(groupTotal, totalHits, sourceAsMap);
//    }
//
//    public List<AggregationQueryKeyValue<List<String>, Long>> getTesteeInfoForPagination(int offset, int limit, List<FieldSortBuilder> sorts) throws Exception {
//        List<AggregationQueryKeyValue<List<String>, Long>> resultList = new ArrayList<>();
//        int size = offset + limit;
//        //传入一下新的聚合函数进行聚合,使用bucket_sort实现
//        TermsAggregationBuilder groupByFieldAgg = AggregationBuilders.terms("group_by_field").script(queryScript).size(size);
//        //定义分页条件
//        BucketSortPipelineAggregationBuilder bucketSort = new BucketSortPipelineAggregationBuilder("bucket_sort", sorts).from(offset).size(limit);
//        //添加分页内容
//        groupByFieldAgg.subAggregation(bucketSort);
//        //指定agg
//        searchSourceBuilder.aggregation(groupByFieldAgg);
//        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
//        Terms terms = searchResponse.getAggregations().get("group_by_field");
//        List<? extends Terms.Bucket> buckets = terms.getBuckets();
//        for (Terms.Bucket bucketItem : buckets) {
//            String[] keyArray = bucketItem.getKeyAsString().split(spiltSymbol);
//            resultList.add(new AggregationQueryKeyValue<>(Arrays.asList(keyArray), bucketItem.getDocCount()));
//        }
//        return resultList;
//    }
//}
