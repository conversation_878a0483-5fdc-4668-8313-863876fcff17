package com.haoys.user.mapper;

import com.haoys.user.model.SecureAppRequestLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 安全应用请求日志Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Mapper
public interface SecureAppRequestLogMapper {
    
    /**
     * 根据主键查询
     */
    SecureAppRequestLog selectByPrimaryKey(Long id);
    
    /**
     * 插入记录
     */
    int insert(SecureAppRequestLog record);
    
    /**
     * 选择性插入记录
     */
    int insertSelective(SecureAppRequestLog record);
    
    /**
     * 根据条件查询日志列表
     */
    List<SecureAppRequestLog> selectByCondition(@Param("appId") String appId, 
                                               @Param("environment") String environment,
                                               @Param("requestType") String requestType,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计今日请求次数
     */
    int countTodayRequests(@Param("appId") String appId, @Param("environment") String environment);
    
    /**
     * 统计指定时间范围内的请求次数
     */
    int countRequestsByTimeRange(@Param("appId") String appId, 
                                @Param("environment") String environment,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的日志
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);
}
