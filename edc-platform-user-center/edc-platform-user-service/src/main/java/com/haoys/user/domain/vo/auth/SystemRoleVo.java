package com.haoys.user.domain.vo.auth;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SystemRoleVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "英文名称")
    private String englishName;

    @ApiModelProperty(value = "系统用户数量")
    private Integer adminCount;

    @ApiModelProperty(value = "是否系统默认角色0/1")
    private Boolean systemDefault;

    @ApiModelProperty(value = "数据状态0/1")
    private Integer status;

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "菜单集合")
    private Long[] menuIds;

    @ApiModelProperty(value = "菜单树选择项是否关联显示 0-父子不互相关联显示 1-父子互相关联显示")
    private boolean menuCheckStrictly;
}
