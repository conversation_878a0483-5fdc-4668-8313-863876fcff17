package com.haoys.user.mapper;

import com.haoys.user.model.SystemUserPwd;
import com.haoys.user.model.SystemUserPwdExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SystemUserPwdMapper {
    long countByExample(SystemUserPwdExample example);

    int deleteByExample(SystemUserPwdExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemUserPwd record);

    int insertSelective(SystemUserPwd record);

    List<SystemUserPwd> selectByExample(SystemUserPwdExample example);

    SystemUserPwd selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemUserPwd record, @Param("example") SystemUserPwdExample example);

    int updateByExample(@Param("record") SystemUserPwd record, @Param("example") SystemUserPwdExample example);

    int updateByPrimaryKeySelective(SystemUserPwd record);

    int updateByPrimaryKey(SystemUserPwd record);
}
