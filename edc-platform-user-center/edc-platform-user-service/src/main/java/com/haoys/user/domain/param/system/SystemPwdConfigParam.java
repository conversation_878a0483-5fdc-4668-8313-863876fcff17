package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SystemPwdConfigParam {

    private Integer id;

    @ApiModelProperty(value = "最小密码长度（位）")
    private Integer minPwdLength;

    @ApiModelProperty(value = "最大密码长度（位）")
    private Integer maxPwdLength;

    @ApiModelProperty(value = "密码包含数字")
    private Boolean isContainsNumber;

    @ApiModelProperty(value = "密码有效期（天）")
    private Integer validlyDay;

    @ApiModelProperty(value = "密码包含大小写")
    private Boolean isContainsCase;

    @ApiModelProperty(value = "密码连续错误次数锁定（次）")
    private Integer pwdLockNum;

    @ApiModelProperty(value = "密码包含特殊字符")
    private Boolean isContainsSpecial;

    @ApiModelProperty(value = "密码历史不可重复（次）")
    private Integer nonRepeatableNum;

    @ApiModelProperty(value = "长时间不操作登出（分钟）")
    private Integer expiresTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

}
