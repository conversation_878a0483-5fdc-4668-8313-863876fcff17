package com.haoys.user.domain.vo.rcts;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/15 15:02
 */
@Data
public class RandomizedBlindRecordVo {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "项目研究中心名称")
    private String projectOrgName;

    @ApiModelProperty(value = "区组号")
    private Integer batchNo;

    @ApiModelProperty(value = "区组内编号")
    private Integer batchItemNo;

    @ApiModelProperty(value = "随机号")
    private String randomizedNumber;

    @ApiModelProperty(value = "组别名称")
    private String joinGroupName;

    @ApiModelProperty(value = "随机时间")
    private Date randomizedTime;

    @ApiModelProperty(value = "扩展参数")
    private String expand;

    @ApiModelProperty(value = "扩展参数")
    private Object expandObject;

    @ApiModelProperty(value = "受试者绑定id")
    private String bindTesteeId;

    @ApiModelProperty(value = "受试者绑定随机号时间")
    private Date bindRandomizedTime;

    @ApiModelProperty(value = "研究状态")
    private String researchStatus;

}
