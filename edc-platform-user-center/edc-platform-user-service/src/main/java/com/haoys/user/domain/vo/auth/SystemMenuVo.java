package com.haoys.user.domain.vo.auth;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SystemMenuVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "菜单名称")
    private String title;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;

    @ApiModelProperty(value = "路由地址")
    private String path;

    @ApiModelProperty(value = "组件路径")
    private String component;

    @ApiModelProperty(value = "权限标识")
    private String perms;

    @ApiModelProperty(value = "菜单级数（0-目录 1-菜单 2-按钮）")
    private Integer level;

    @ApiModelProperty(value = "菜单排序")
    private Integer sort;

    @ApiModelProperty(value = "菜单类型（1-企业管理 2-工作台 3-数据报表 4-企业报表 5-平台管理）")
    private String menuType;

    @ApiModelProperty(value = "前端页面路由匹配值")
    private String name;

    @ApiModelProperty(value = "是否为系统默认菜单 1-true 0-false")
    private Boolean systemDefault;

    @ApiModelProperty(value = "项目定制角色设置是否显示，CRA不显示新增参与者")
    private String customRoleHidden;

    @ApiModelProperty(value = "菜单分组")
    private String groupInfo;

    @ApiModelProperty(value = "菜单分组id")
    private String groupId;

    @ApiModelProperty(value = "前端图标")
    private String icon;

    @ApiModelProperty(value = "前端隐藏 0-显示 1-隐藏")
    private Integer hidden;

    @ApiModelProperty(value = "菜单状态（0正常 1停用）")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "菜单说明")
    private String description;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private List<SystemMenuVo> childrens = new ArrayList<SystemMenuVo>();


}
