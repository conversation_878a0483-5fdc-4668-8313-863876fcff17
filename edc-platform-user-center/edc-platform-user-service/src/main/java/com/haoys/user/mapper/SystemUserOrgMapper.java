package com.haoys.user.mapper;

import com.haoys.user.model.SystemUserOrg;
import com.haoys.user.model.SystemUserOrgExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemUserOrgMapper {
    long countByExample(SystemUserOrgExample example);

    int deleteByExample(SystemUserOrgExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemUserOrg record);

    int insertSelective(SystemUserOrg record);

    List<SystemUserOrg> selectByExample(SystemUserOrgExample example);

    SystemUserOrg selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemUserOrg record, @Param("example") SystemUserOrgExample example);

    int updateByExample(@Param("record") SystemUserOrg record, @Param("example") SystemUserOrgExample example);

    int updateByPrimaryKeySelective(SystemUserOrg record);

    int updateByPrimaryKey(SystemUserOrg record);

    void deleteSystemUserOrgInfo(String userId);
}