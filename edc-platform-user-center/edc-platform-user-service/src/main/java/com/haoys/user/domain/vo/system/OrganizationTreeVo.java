package com.haoys.user.domain.vo.system;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationTreeVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "机构中心id")
    private Long id;
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "父节点")
    private Long parentId;
    @ApiModelProperty(value = "机构中心名称")
    private String name;
    @ApiModelProperty(value = "机构层级")
    private String path;
    @ApiModelProperty(value = "是否叶子节点")
    private Boolean leafNode = true;
    @ApiModelProperty(value = "是否主机构")
    private Boolean ifPrimary = true;
//    @ApiModelProperty(value = "当前节点的子节点集合")
//    private List<OrganizationTreeVo> childrenList = new ArrayList<>();


}
