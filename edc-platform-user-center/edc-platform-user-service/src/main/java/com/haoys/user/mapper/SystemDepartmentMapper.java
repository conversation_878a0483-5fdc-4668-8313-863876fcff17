package com.haoys.user.mapper;

import com.haoys.user.model.SystemDepartment;
import com.haoys.user.model.SystemDepartmentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemDepartmentMapper {
    long countByExample(SystemDepartmentExample example);

    int deleteByExample(SystemDepartmentExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemDepartment record);

    int insertSelective(SystemDepartment record);

    List<SystemDepartment> selectByExample(SystemDepartmentExample example);

    SystemDepartment selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemDepartment record, @Param("example") SystemDepartmentExample example);

    int updateByExample(@Param("record") SystemDepartment record, @Param("example") SystemDepartmentExample example);

    int updateByPrimaryKeySelective(SystemDepartment record);

    int updateByPrimaryKey(SystemDepartment record);

    /**
     * 根据机构ID查询数据
     *
     * @param orgId
     * @return
     */
    SystemDepartment selectByOrgId(Long orgId);
}