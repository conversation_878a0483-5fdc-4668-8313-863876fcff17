package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.entity.SystemOrgInfoQuery;
import com.haoys.user.domain.vo.project.SysOrgExportVo;
import com.haoys.user.domain.vo.project.SysOrgResult;
import com.haoys.user.domain.vo.system.SystemOrgInfoVo;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.model.SystemOrgInfoExample;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface SystemOrgInfoService {

    /**
     * 根据条件分页查询系统中心机构信息数据
     */
    CommonPage<SystemOrgInfoVo> selectSystemOrgInfoList(SystemOrgInfoQuery systemOrgInfoQuery);

    /**
     * 新增保存系统中心机构信息
     */
    CustomResult saveSystemOrgInfo(SystemOrgInfoQuery systemOrgInfoQuery);

    /**
     * 根据orgId更新所属地区
     * @param systemOrgInfoQuery
     * @return
     */
    CustomResult updateSystemOrgAreaInfoByOrgId(SystemOrgInfoQuery systemOrgInfoQuery);
    
    CustomResult updateSystemOrgAreaInfoByOrgId(SystemOrgInfo systemOrgInfo);

    /**
     * 修改保存系统中心机构信息
     * @return
     */
    CustomResult updateSystemOrgInfo(SystemOrgInfoQuery systemOrgInfoQuery);

    /**
     * 通过系统中心机构信息ID删除
     * @return
     */
    CustomResult deleteSystemOrgInfoByOrgId(Long orgId);

    /**
     * 根据ID查询系统中心机构信息
     */
    SystemOrgInfo selectSystemOrgInfoByOrgId(String orgId);

    /**
     * 根据名称查询中心
     */
    SystemOrgInfoVo selectSystemOrgInfoByOrgName(String orgName);

    /**
     * 导入数据
     */
    CustomResult<SysOrgResult> saveBatchSystemOrgInfo(MultipartFile file, List<SysOrgExportVo> errorList) throws Exception;

    /**
     * 查询第三方系统结构中心名称
     * @param orgName
     * @return
     */
    Map<String, Object> getThirdSystemOrgInfoByName(String orgName);

    List<SystemOrgInfo> selectByExample(SystemOrgInfoExample example);

    int enableOrUnable(String id);


}
