package com.haoys.user.domain.enums;

import com.haoys.user.common.api.BaseResultCode;

/**
 * 邀请用户激活校验返回类
 */
public enum SystemUserReturnEnums implements BaseResultCode {
    E20003(20003,"密码错误"),
    E20004(20005,"手机号有误"),
    E20006(20006,"请输入正确验证码"),
    E20010(20010,"密码长度8-16位"),
    E20011(20011,"密码需要包含大小写字母"),
    E20012(20012,"密码需要包含数字、大小写字母、特殊符号"),
    E20013(20013,"邮箱格式有误"),
    E20014(20014,"邮箱已注册"),
    E20015(20015,"手机号已注册"),
    E20017(20017,"登录名已被使用"),
    E500(500,"操作失败"),
    S200(200,"操作成功");

    private int code;

    private String message;

    SystemUserReturnEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
