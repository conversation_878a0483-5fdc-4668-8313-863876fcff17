package com.haoys.user.mapper;


import com.haoys.user.domain.param.dict.DictParamQuery;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.DictionaryExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DictionaryMapper {
    long countByExample(DictionaryExample example);

    int deleteByExample(DictionaryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(Dictionary record);

    int insertSelective(Dictionary record);

    List<Dictionary> selectByExample(DictionaryExample example);

    Dictionary selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") Dictionary record, @Param("example") DictionaryExample example);

    int updateByExample(@Param("record") Dictionary record, @Param("example") DictionaryExample example);

    int updateByPrimaryKeySelective(Dictionary record);

    int updateByPrimaryKey(Dictionary record);

    int deleteByCode(String code);

    Dictionary getDictinaryByName(String dictName);

    /**
     * 获取字典列表
     * @param param 搜索参数
     * @return
     */
    List<Dictionary> selectList(DictParamQuery param);

}
