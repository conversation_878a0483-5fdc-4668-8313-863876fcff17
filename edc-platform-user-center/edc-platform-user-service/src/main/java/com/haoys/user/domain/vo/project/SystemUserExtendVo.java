package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
public class SystemUserExtendVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "预留第三方登录对接id")
    private String thirdOpenId;

    @ApiModelProperty(value = "bdp用户id")
    private String bpdUserId;

    @ApiModelProperty(value = "备注信息")
    private String note;

    @ApiModelProperty(value = "启用、停用")
    private Boolean sealFlag;

    @ApiModelProperty(value = "帐号启用状态：0->禁用；1->启用")
    private Integer status;

    @ApiModelProperty(value = "身份类型 1-项目负责人 2-医生 3-CRC 4-数据经理 5-医学经理")
    private String identityType;

    @ApiModelProperty(value = "用户类型（0系统后台用户1:前台添加用户，2：自己注册登录用户）")
    private String userType;

    @ApiModelProperty(value = "单位id")
    private String department;

    @ApiModelProperty(value = "单位名称")
    private String departmentName;

    @ApiModelProperty(value = "职称")
    private String positional;

    @ApiModelProperty(value = "职称名称")
    private String positionalName;

    @ApiModelProperty(value = "用户所在机构中心id")
    private String orgId;

    @ApiModelProperty(value = "用户所在机构中心信息")
    private String orgName;

    @ApiModelProperty(value = "是否用户主机构")
    private Boolean ifPrimary=true;

    @ApiModelProperty(value = "加入项目数量")
    private int projectCount = 0;

    @ApiModelProperty(value = "角色名称")
    private String roleName = "";

    @ApiModelProperty(value = "角色英文名称")
    private String roleEName = "";

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
