package com.haoys.user.domain.vo.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SystemLoginLogVo {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "用户账号")
    private String userName;

    @ApiModelProperty(value = "登录IP地址")
    private String requestIp;

    @ApiModelProperty(value = "登录地点")
    private String location;

    @ApiModelProperty(value = "浏览器类型")
    private String browser;

    @ApiModelProperty(value = "操作系统")
    private String os;

    @ApiModelProperty(value = "登录状态0/1")
    private String status;

    @ApiModelProperty(value = "返回消息")
    private String message;

    @ApiModelProperty(value = "访问时间")
    private Date loginTime;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "操作类型 1-系统登录 2-超时或者更换登录设备 3-系统推出'")
    private String operateType;

    private String realName;
    private String orgName;
}
