package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class FindSystemUserPasswordParam {

    @NotEmpty(message = "账号类型不能为空")
    @ApiModelProperty(value = "账号类型 1-用户名 2-手机号 3-邮箱", required = true)
    private String accountType = "1";

    @NotEmpty(message = "用户名不能为空")
    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @NotEmpty(message = "验证码不能为空")
    @ApiModelProperty(value = "验证码",required = true)
    private String code = "";

    @NotEmpty
    @ApiModelProperty(value = "新密码",required = true)
    private String newPassword;

    @NotEmpty(message = "企业租户不能为空")
    @ApiModelProperty(value = "企业租户id",required = true)
    private String tenantId;

    @NotEmpty(message = "平台id不能为空")
    @ApiModelProperty(value = "平台id",required = true)
    private String platformId;
}
