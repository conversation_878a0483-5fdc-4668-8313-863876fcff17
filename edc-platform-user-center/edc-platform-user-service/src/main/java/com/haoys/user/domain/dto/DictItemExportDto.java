package com.haoys.user.domain.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;


@Data
@ExcelTarget("dictItemExportDto")
public class DictItemExportDto {

    @Excel(name = "序号", format = "isAddIndex")
    private Integer xh;
    @Excel(name = "字典名称",height = 20,width = 30, isImportField = "true_st")
    private String name;

    @Excel(name = "字典编码", height = 20,width = 30, isImportField = "true_st")
    private String code;

    @Excel(name = "选项名称",height = 20,width = 30, isImportField = "true_st")
    private String optionName;

    @Excel(name = "英文名称",height = 20,width = 30, isImportField = "true_st")
    private String enName;

    @Excel(name = "选项值",height = 20,width = 30, isImportField = "true_st")
    private String optionValue;
}
