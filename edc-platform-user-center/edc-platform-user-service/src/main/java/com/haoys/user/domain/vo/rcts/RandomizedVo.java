package com.haoys.user.domain.vo.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/3 14:48
 */
@Data
public class RandomizedVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "方案id")
    private Long planId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单id")
    private Long formId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "变量id")
    private Long variableId;

    @ApiModelProperty(value = "变量名称")
    private String variableName;

    @ApiModelProperty(value = "变量code")
    private String variableCode;

    @ApiModelProperty(value = "扩展参数")
    private String expand;

    @ApiModelProperty(value = "扩展参数对象")
    private Object expandObject;

}
