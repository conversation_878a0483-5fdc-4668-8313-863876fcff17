package com.haoys.user.domain.vo.project;


import com.haoys.user.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SysOrgExcelVo implements Serializable {

    @Excel(name = "中心名称")
    private String orgName;

    @Excel(name = "医院等级")
    private String orgType;

    @Excel(name = "省（直辖市）")
    private String provinceName;

    @Excel(name = "市（区）")
    private String cityName;

    @Excel(name = "县（区）")
    private String countyName;

    @ApiModelProperty(value = "省code")
    private Long provinceCode;

    @ApiModelProperty(value = "市code")
    private Long cityCode;

    @ApiModelProperty(value = "县code")
    private Long countyCode;

}
