package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * 修改用户名密码参数
 * Created by macro on 2019/10/9.
 */
@Getter
@Setter
public class UpdateSystemUserPasswordParam {

    @NotEmpty
    @ApiModelProperty(value = "1-修改密码 2-重置密码 3-患者端找回密码", required = true)
    private String type;

    @NotEmpty
    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "旧密码")
    private String oldPassword;

    @ApiModelProperty(value = "新密码")
    private String newPassword;

    @ApiModelProperty(value = "自定义密码")
    private String initPassword;

    @ApiModelProperty(value = "操作人id")
    private String createUserId;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;
}
