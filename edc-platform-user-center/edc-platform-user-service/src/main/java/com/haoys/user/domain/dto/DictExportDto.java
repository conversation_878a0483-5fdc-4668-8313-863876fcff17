package com.haoys.user.domain.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;


@Data
@ExcelTarget("dictExportVo")
public class DictExportDto {

    @Excel(name = "序号", format = "isAddIndex")
    private Integer xh;
    @Excel(name = "字典名称", width = 30, isImportField = "true_st")
    private String name;

    @Excel(name = "字典编码", width = 30, isImportField = "true_st")
    private String code;

    @Excel(name = "字典类型",width = 30, replace = { "项目表单字典_1","数据单位字典_2" },  isImportField = "true_st")
    private int dicType;

    @Excel(name = "字典描述",width = 30, isImportField = "true_st")
    private String description;

    @Excel(name = "状态", replace = { "启用_0","停用_1" },  isImportField = "true_st")
    private String status;
}
