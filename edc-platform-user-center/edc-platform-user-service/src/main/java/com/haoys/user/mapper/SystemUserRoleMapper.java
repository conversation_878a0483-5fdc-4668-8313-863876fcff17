package com.haoys.user.mapper;

import com.haoys.user.model.SystemUserRole;
import com.haoys.user.model.SystemUserRoleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemUserRoleMapper {
    long countByExample(SystemUserRoleExample example);

    int deleteByExample(SystemUserRoleExample example);

    int deleteByPrimaryKey(@Param("userId") Long userId, @Param("roleId") Long roleId);

    int insert(SystemUserRole record);

    int insertSelective(SystemUserRole record);

    List<SystemUserRole> selectByExample(SystemUserRoleExample example);

    SystemUserRole selectByPrimaryKey(@Param("userId") Long userId, @Param("roleId") Long roleId);

    int updateByExampleSelective(@Param("record") SystemUserRole record, @Param("example") SystemUserRoleExample example);

    int updateByExample(@Param("record") SystemUserRole record, @Param("example") SystemUserRoleExample example);

    int updateByPrimaryKeySelective(SystemUserRole record);

    int updateByPrimaryKey(SystemUserRole record);

    int batchSaveUserRole(List<SystemUserRole> userRoleList);

    int deleteSystemUserRoleByUserId(Long userId, String tenantId, String platformId);
}