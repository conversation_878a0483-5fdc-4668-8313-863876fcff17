package com.haoys.user.domain.dto;

import java.io.Serializable;

public class BdpUserBaseInfo implements Serializable {

    private static final long serialVersionUID = -112411087491977794L;

    private String loginToken;

    private BdpLoginUserResultDto user;

    public String getLoginToken() {
        return loginToken;
    }

    public void setLoginToken(String loginToken) {
        this.loginToken = loginToken;
    }

    public BdpLoginUserResultDto getUser() {
        return user;
    }

    public void setUser(BdpLoginUserResultDto user) {
        this.user = user;
    }
}
