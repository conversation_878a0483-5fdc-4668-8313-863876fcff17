package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class SystemUserInfoBack implements Serializable {
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "用户头像")
    private String icon;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户注册来源")
    private String userType;

    @ApiModelProperty(value = "用户姓名")
    private String realName;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;

    @ApiModelProperty(value = "预留第三方登录对接id")
    private String thirdOpenId;

    @ApiModelProperty(value = "bdp用户id")
    private String bpdUserId;

    @ApiModelProperty(value = "备注信息")
    private String note;

    @ApiModelProperty(value = "系统角色描述信息-PM等")
    private String roleDesc;

    @ApiModelProperty(value = "token信息")
    private String tokenValue;

    @ApiModelProperty(value = "token过期时间")
    private Date expireTime;

    @ApiModelProperty(value = "登录code")
    private String loginCode;

    @ApiModelProperty(value = "数据状态 0/1")
    private Boolean sealFlag;

    @ApiModelProperty(value = "启用状态0/1")
    private Integer status;

    @ApiModelProperty(value = "激活状态0/1")
    private Boolean activeStatus;

    @ApiModelProperty(value = "工作单位-参照系统中心id")
    private String department;

    @ApiModelProperty(value = "职称-参照系统字典")
    private String positional;

    @ApiModelProperty(value = "企业部门")
    private String enterprise;

    @ApiModelProperty(value = "工作地址")
    private String address;

    @ApiModelProperty(value = "创建项目数量限制")
    private Integer projectCountLimit;

    @ApiModelProperty(value = "项目新增参与者限制")
    private Integer projectTesteeLimit;

    @ApiModelProperty(value = "是否为运营方项目管理员")
    private Boolean companyOwnerUser;

    @ApiModelProperty(value = "默认企业管理员标识")
    private Boolean defaultAdmin;

    @ApiModelProperty(value = "身份类型 1-项目负责人 2-医生 3-CRC 4-数据经理 5-医学经理")
    private String identityType;

    @ApiModelProperty(value = "注册来源")
    private String registerFrom;

    @ApiModelProperty(value = "登录设备")
    private String loginDevice;

    @ApiModelProperty(value = "所属管理员-用户产品推广")
    private Long parentUser;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "最后修改密码时间")
    private Date lastModifyPwdTime;

    @ApiModelProperty(value = "最新登录时间")
    private Date loginTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getThirdOpenId() {
        return thirdOpenId;
    }

    public void setThirdOpenId(String thirdOpenId) {
        this.thirdOpenId = thirdOpenId;
    }

    public String getBpdUserId() {
        return bpdUserId;
    }

    public void setBpdUserId(String bpdUserId) {
        this.bpdUserId = bpdUserId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getRoleDesc() {
        return roleDesc;
    }

    public void setRoleDesc(String roleDesc) {
        this.roleDesc = roleDesc;
    }

    public String getTokenValue() {
        return tokenValue;
    }

    public void setTokenValue(String tokenValue) {
        this.tokenValue = tokenValue;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getLoginCode() {
        return loginCode;
    }

    public void setLoginCode(String loginCode) {
        this.loginCode = loginCode;
    }

    public Boolean getSealFlag() {
        return sealFlag;
    }

    public void setSealFlag(Boolean sealFlag) {
        this.sealFlag = sealFlag;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Boolean activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPositional() {
        return positional;
    }

    public void setPositional(String positional) {
        this.positional = positional;
    }

    public String getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(String enterprise) {
        this.enterprise = enterprise;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getProjectCountLimit() {
        return projectCountLimit;
    }

    public void setProjectCountLimit(Integer projectCountLimit) {
        this.projectCountLimit = projectCountLimit;
    }

    public Integer getProjectTesteeLimit() {
        return projectTesteeLimit;
    }

    public void setProjectTesteeLimit(Integer projectTesteeLimit) {
        this.projectTesteeLimit = projectTesteeLimit;
    }

    public Boolean getCompanyOwnerUser() {
        return companyOwnerUser;
    }

    public void setCompanyOwnerUser(Boolean companyOwnerUser) {
        this.companyOwnerUser = companyOwnerUser;
    }

    public Boolean getDefaultAdmin() {
        return defaultAdmin;
    }

    public void setDefaultAdmin(Boolean defaultAdmin) {
        this.defaultAdmin = defaultAdmin;
    }

    public String getIdentityType() {
        return identityType;
    }

    public void setIdentityType(String identityType) {
        this.identityType = identityType;
    }

    public String getRegisterFrom() {
        return registerFrom;
    }

    public void setRegisterFrom(String registerFrom) {
        this.registerFrom = registerFrom;
    }

    public String getLoginDevice() {
        return loginDevice;
    }

    public void setLoginDevice(String loginDevice) {
        this.loginDevice = loginDevice;
    }

    public Long getParentUser() {
        return parentUser;
    }

    public void setParentUser(Long parentUser) {
        this.parentUser = parentUser;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getLastModifyPwdTime() {
        return lastModifyPwdTime;
    }

    public void setLastModifyPwdTime(Date lastModifyPwdTime) {
        this.lastModifyPwdTime = lastModifyPwdTime;
    }

    public Date getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Date loginTime) {
        this.loginTime = loginTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", username=").append(username);
        sb.append(", password=").append(password);
        sb.append(", icon=").append(icon);
        sb.append(", email=").append(email);
        sb.append(", userType=").append(userType);
        sb.append(", realName=").append(realName);
        sb.append(", nickName=").append(nickName);
        sb.append(", mobile=").append(mobile);
        sb.append(", thirdOpenId=").append(thirdOpenId);
        sb.append(", bpdUserId=").append(bpdUserId);
        sb.append(", note=").append(note);
        sb.append(", roleDesc=").append(roleDesc);
        sb.append(", tokenValue=").append(tokenValue);
        sb.append(", expireTime=").append(expireTime);
        sb.append(", loginCode=").append(loginCode);
        sb.append(", sealFlag=").append(sealFlag);
        sb.append(", status=").append(status);
        sb.append(", activeStatus=").append(activeStatus);
        sb.append(", department=").append(department);
        sb.append(", positional=").append(positional);
        sb.append(", enterprise=").append(enterprise);
        sb.append(", address=").append(address);
        sb.append(", projectCountLimit=").append(projectCountLimit);
        sb.append(", projectTesteeLimit=").append(projectTesteeLimit);
        sb.append(", companyOwnerUser=").append(companyOwnerUser);
        sb.append(", defaultAdmin=").append(defaultAdmin);
        sb.append(", identityType=").append(identityType);
        sb.append(", registerFrom=").append(registerFrom);
        sb.append(", loginDevice=").append(loginDevice);
        sb.append(", parentUser=").append(parentUser);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", lastModifyPwdTime=").append(lastModifyPwdTime);
        sb.append(", loginTime=").append(loginTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}