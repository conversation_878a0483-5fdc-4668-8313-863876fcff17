package com.haoys.user.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DepartmentParam {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "科室名称")
    private String name;

    @ApiModelProperty(value = "中心id")
    private Long orgId;

    @ApiModelProperty(value = "中心名称")
    private String orgName;

    @ApiModelProperty(value = "数据状态0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "操作人")
    private String createUser;

}
