package com.haoys.user.domain.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class SystemDepartmentVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "科室名称")
    private String name;

    @ApiModelProperty(value = "负责人")
    private String personCharge;

    @ApiModelProperty(value = "部门描述")
    private String description;

    @ApiModelProperty(value = "中心id")
    private Long orgId;

    @ApiModelProperty(value = "中心名称")
    private String orgName;

    @ApiModelProperty(value = "数据状态0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "部门人员数")
    private int countUser;
}
