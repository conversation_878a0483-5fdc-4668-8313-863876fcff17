package com.haoys.user.mapper;

import com.haoys.user.domain.vo.auth.SystemMenuVo;
import com.haoys.user.model.SystemMenu;
import com.haoys.user.model.SystemMenuExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SystemMenuMapper {

    long countByExample(SystemMenuExample example);

    int deleteByExample(SystemMenuExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemMenu record);

    int insertSelective(SystemMenu record);

    List<SystemMenu> selectByExample(SystemMenuExample example);

    SystemMenu selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemMenu record, @Param("example") SystemMenuExample example);

    int updateByExample(@Param("record") SystemMenu record, @Param("example") SystemMenuExample example);

    int updateByPrimaryKeySelective(SystemMenu record);

    int updateByPrimaryKey(SystemMenu record);

    List<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据用户ID查询所属项目权限
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectMenuPermsByProject(Long userId);

    /**
     * 根据项目ID获取所属项目权限
     * @param projectId 项目ID
     * @return
     */
    List<SystemMenu> selectListByProjectId(Long projectId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @param menuCheckStrictly 菜单树选择项是否关联显示
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

    /**
     * 根据项目角色ID查询项目菜单树信息
     * @param roleId 角色ID
     * @param menuCheckStrictly 菜单树选择项是否关联显示
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByProjectRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);


    /**
     * 根据PA获取项目权限菜单
     * @param projectId 项目ID
     * @param userId    用户ID
     * @param parentId
     * @param groupName
     * @param roleId
     * @return
     */
    List<SystemMenu> selectProjectUserMenuListByUserId(@Param("projectId") Long projectId, @Param("userId") Long userId,
                                                       @Param("parentId") Long parentId, @Param("groupName") String groupName,
                                                       @Param("roleId") String roleId);


    /**
     * 根据当前登录用户获取项目权限菜单
     * @param projectId
     * @param userId
     * @param parentId
     * @param groupName
     * @param roleId
     * @return
     */
    List<SystemMenu> selectProjectOrgUserMenuListByUserId(@Param("projectId") Long projectId, @Param("userId") Long userId,
                                                          @Param("parentId") Long parentId, @Param("groupName") String groupName,
                                                          @Param("roleId") String roleId);

    /**
     * 查询企业用户权限列表
     * @param userId
     * @param groupName
     * @return
     */
    List<SystemMenu> getSystemUserMenuList(@Param("userId")String userId, @Param("menuType")String menuType, @Param("groupName")String groupName);


    /**
     * 查询系统平台菜单-顶级菜单
     * @param userId
     * @param tenantId
     * @return
     */
    List<SystemMenuVo> getSystemPlatformMenuNameList(@Param("userId") String userId, @Param("tenantId") String tenantId);


}
