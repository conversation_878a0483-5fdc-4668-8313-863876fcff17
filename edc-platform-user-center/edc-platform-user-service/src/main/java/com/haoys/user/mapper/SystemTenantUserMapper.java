package com.haoys.user.mapper;

import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemTenantUserExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemTenantUserMapper {
    long countByExample(SystemTenantUserExample example);

    int deleteByExample(SystemTenantUserExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemTenantUser record);

    int insertSelective(SystemTenantUser record);

    List<SystemTenantUser> selectByExample(SystemTenantUserExample example);

    SystemTenantUser selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemTenantUser record, @Param("example") SystemTenantUserExample example);

    int updateByExample(@Param("record") SystemTenantUser record, @Param("example") SystemTenantUserExample example);

    int updateByPrimaryKeySelective(SystemTenantUser record);

    int updateByPrimaryKey(SystemTenantUser record);
    
    SystemTenantUser getSystemTenantUserByUserId(String userId, String tenantId, String platformId);
}