package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = false)
public class UserVerificationCodeParam {

    @NotEmpty
    @ApiModelProperty(value = "手机号",required = true)
    private String mobile;
    
    @NotEmpty
    @ApiModelProperty(value = "验证码",required = true)
    private String code;
    
    @NotEmpty
    @ApiModelProperty(value = "登录来源",required = true)
    private String loginSource;

    @ApiModelProperty(value = "患者标识 1-是 0-否",required = true)
    private String testeeValue = "0";

    @ApiModelProperty(value = "企业租户id",required = true)
    private String tenantId = "";

    @ApiModelProperty(value = "系统平台id",required = true)
    private String platformId = "";
}
