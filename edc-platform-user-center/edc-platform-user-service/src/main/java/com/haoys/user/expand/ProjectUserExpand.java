package com.haoys.user.expand;

import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectUserExpand extends SystemUserInfoVo {
    
    
    private String projectOrgId;
    private String testeeId;
    private String testeeCode;
    private String acronym;
    private String regionName;
    private String groupName;
    
    
    @ApiModelProperty(value = "姓名")
    private String realName;
    
    @ApiModelProperty(value = "区域")
    private String area;
    
    @ApiModelProperty(value = "组")
    private String groupInfo;
    
    @ApiModelProperty(value = "医院")
    private String hospital;
    
    @ApiModelProperty(value = "科室")
    private String dept;
    
    private String projectVolunteer;
    
    
    private List<DynamicColumnHeadRowData> DynamicColumnHeadRowList = new ArrayList<>();
    private int testee_count;
    
    @Data
    public static class DynamicColumnHeadRowData{

        private String visitId;
        private String visitName;

        private int total_count;
        private int submit_count;
        private int check_count;

        // 新增详细统计字段
        @ApiModelProperty(value = "总表单数")
        private Integer totalForms;

        @ApiModelProperty(value = "已提交表单数")
        private Integer submittedForms;

        @ApiModelProperty(value = "已通过审核表单数")
        private Integer approvedForms;

        @ApiModelProperty(value = "已拒绝表单数")
        private Integer rejectedForms;

        @ApiModelProperty(value = "待审核表单数")
        private Integer pendingForms;

    }
    
}





