package com.haoys.user.domain.vo.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/14 13:19
 */
@Data
public class DrugDistributeVo {

    //@JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "药品id")
    private Long drugId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "受试者id")
    private Long testeeId;

    @ApiModelProperty(value = "受试者code")
    private String testeeCode;

    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "发放数量")
    private Integer distributeCount;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "发放时间")
    private Date distributeTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "使用开始日期")
    private Date usageStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "使用截止日期")
    private Date usageEndTime;

    @ApiModelProperty(value = "物资编码")
    private String materialCode;

    @ApiModelProperty(value = "产品名")
    private String productName;

    @ApiModelProperty(value = "商品名")
    private String merchandiseName;

    @ApiModelProperty(value = "厂家")
    private String manufacturer;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "包装规格")
    private String materialSpecification;

    @ApiModelProperty(value = "最小单位数量")
    private Integer minPackageUnit;

    @ApiModelProperty(value = "贮藏要求")
    private String storageRequirement;

}
