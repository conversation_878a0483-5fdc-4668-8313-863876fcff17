package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class PatientUserParam {

    @NotEmpty(message = "用户类型不能为空")
    @ApiModelProperty(value = "用户类型 0-系统添加用户 1-项目添加用户 2-登录注册用户 3-患者参与者", required = true)
    private String userType;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;

    @ApiModelProperty(value = "手机验证码")
    private String code;

    @NotEmpty(message = "填写中心不能为空")
    @ApiModelProperty(value = "用户所属中心", required = true)
    private String orgId;
}
