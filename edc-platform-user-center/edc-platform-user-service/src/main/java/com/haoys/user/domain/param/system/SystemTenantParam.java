package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SystemTenantParam {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "系统名称")
    private String systemName;
    @ApiModelProperty(value = "企业名称")
    private String name;
    @ApiModelProperty(value = "企业联系人")
    private String contactName;
    @ApiModelProperty(value = "联系方式")
    private String contactMobile;
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value = "企业介绍")
    private String tenantIntroduce;
    @ApiModelProperty(value = "企业地址")
    private String tenantAddress;
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;
    @ApiModelProperty(value = "电子邮件")
    private String emailAddress;

    @ApiModelProperty(value = "新文件名称")
    private String newFileName;
    @ApiModelProperty(value = "原文件名称")
    private String originalFilename;
    @ApiModelProperty(value = "文件key")
    private String fileNameKey;
    @ApiModelProperty(value = "文件路径")
    private String url;
    @ApiModelProperty(value = "本地路径")
    private String fileName;
}
