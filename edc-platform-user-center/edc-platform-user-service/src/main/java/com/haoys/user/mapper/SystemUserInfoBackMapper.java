package com.haoys.user.mapper;

import com.haoys.user.model.SystemUserInfoBack;
import com.haoys.user.model.SystemUserInfoBackExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SystemUserInfoBackMapper {
    long countByExample(SystemUserInfoBackExample example);

    int deleteByExample(SystemUserInfoBackExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemUserInfoBack record);

    int insertSelective(SystemUserInfoBack record);

    List<SystemUserInfoBack> selectByExample(SystemUserInfoBackExample example);

    SystemUserInfoBack selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemUserInfoBack record, @Param("example") SystemUserInfoBackExample example);

    int updateByExample(@Param("record") SystemUserInfoBack record, @Param("example") SystemUserInfoBackExample example);

    int updateByPrimaryKeySelective(SystemUserInfoBack record);

    int updateByPrimaryKey(SystemUserInfoBack record);
}
