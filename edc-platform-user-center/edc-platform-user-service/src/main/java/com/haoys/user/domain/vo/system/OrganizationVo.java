package com.haoys.user.domain.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrganizationVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "系统中心id")
    private Long id;

    @ApiModelProperty(value = "机构中心名称")
    private String name;

    @ApiModelProperty(value = "中心识别码")
    private String identCode;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "父节点")
    private Long parentId;

    @ApiModelProperty(value = "项目研究中心code")
    private String projectOrgCode;

    @ApiModelProperty(value = "项目研究中心id")
    private String projectOrgId;

    @ApiModelProperty(value = "项目研究中心是否选中")
    private Boolean selected = false;

    @ApiModelProperty(value = "code码值")
    private String systemCode;

    @ApiModelProperty(value = "机构来源")
    private String resource;

    @ApiModelProperty(value = "数据状态")
    private String sealFlag;

}

