package com.haoys.user.domain.enums;

import com.haoys.user.common.api.BaseResultCode;

public enum SystemDictEnums implements BaseResultCode {
    E51000(51000,"字典名称/字典编码已被使用"),
    E51001(51001,"选项名称/选项值/序号已被使用"),
    E51002(51002,"删除失败，当前字典已被使用"),
    E51003(51003,"禁用失败，当前字典已被使用"),
    E51004(51004,"删除失败，当前选项已被使用"),
    E70000(70000,"字典名称/字典编码已被使用"),
    E70001(70001,"选项名称/选项值/序号已被使用"),
    E70002(70002,"删除失败，当前字典已被使用"),
    E70003(70003,"禁用失败，当前字典已被使用"),
    E70004(70004,"删除失败，当前选项已被使用");


    private int code;

    private String message;

    SystemDictEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
