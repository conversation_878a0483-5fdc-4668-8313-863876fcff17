package com.haoys.user.mapper;

import com.haoys.user.domain.entity.SystemOrgInfoQuery;
import com.haoys.user.domain.vo.system.SystemOrgInfoVo;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.model.SystemOrgInfoExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SystemOrgInfoMapper {
    long countByExample(SystemOrgInfoExample example);

    int deleteByExample(SystemOrgInfoExample example);

    int deleteByPrimaryKey(Long orgId);

    int insert(SystemOrgInfo record);

    int insertSelective(SystemOrgInfo record);

    List<SystemOrgInfo> selectByExample(SystemOrgInfoExample example);

    SystemOrgInfo selectByPrimaryKey(Long orgId);

    int updateByExampleSelective(@Param("record") SystemOrgInfo record, @Param("example") SystemOrgInfoExample example);

    int updateByExample(@Param("record") SystemOrgInfo record, @Param("example") SystemOrgInfoExample example);

    int updateByPrimaryKeySelective(SystemOrgInfo record);

    int updateByPrimaryKey(SystemOrgInfo record);


    SystemOrgInfoVo getSysOrgInfoByOrgName(String orgName);

    String getMaxOrgCode();

    int insertBatchSysOrgInfo(List<SystemOrgInfoQuery> systemOrgInfoQueryList);

    /**
     * 验证系统中心名称是否存在
     * @param provinceCode
     * @param cityCode
     * @param countyCode
     * @param orgName
     * @return
     */
    SystemOrgInfo getSysOrgInfoByAreaOrgName(Long provinceCode, Long cityCode, Long countyCode, String orgName);

    /**
     * 分页查询系统中心列表
     * @param systemOrgInfoQuery
     * @return
     */
    List<SystemOrgInfoVo> selectSystemOrgListForPage(SystemOrgInfoQuery systemOrgInfoQuery);

}
