package com.haoys.user.service;


import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.dto.DepartmentParam;
import com.haoys.user.domain.dto.DepartmentQuery;
import com.haoys.user.domain.vo.project.SysDepartmentVo;
import com.haoys.user.model.SystemDepartment;
import com.haoys.user.model.SystemDepartmentExample;

import java.util.List;

public interface SystemDepartmentService {

    CommonPage<SysDepartmentVo> getDepartmentListForPage(DepartmentQuery departmentQuery);

    SysDepartmentVo getDepartment(Long id);

    String saveDepartment(DepartmentParam departmentParam);

    String deleteDepartment(Long id);

    List<SystemDepartment> selectByExample(SystemDepartmentExample example);

    int insert(SystemDepartment record);

    SystemDepartment selectByPrimaryKey(Long id);

    int updateByPrimaryKey(SystemDepartment record);

    String deleteByPrimaryKey(Long id);

}
