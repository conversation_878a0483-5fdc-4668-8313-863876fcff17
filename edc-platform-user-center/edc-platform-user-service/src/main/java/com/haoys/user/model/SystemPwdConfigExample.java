package com.haoys.user.model;

import java.util.ArrayList;
import java.util.List;

public class SystemPwdConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SystemPwdConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthIsNull() {
            addCriterion("min_pwd_length is null");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthIsNotNull() {
            addCriterion("min_pwd_length is not null");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthEqualTo(Integer value) {
            addCriterion("min_pwd_length =", value, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthNotEqualTo(Integer value) {
            addCriterion("min_pwd_length <>", value, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthGreaterThan(Integer value) {
            addCriterion("min_pwd_length >", value, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("min_pwd_length >=", value, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthLessThan(Integer value) {
            addCriterion("min_pwd_length <", value, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthLessThanOrEqualTo(Integer value) {
            addCriterion("min_pwd_length <=", value, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthIn(List<Integer> values) {
            addCriterion("min_pwd_length in", values, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthNotIn(List<Integer> values) {
            addCriterion("min_pwd_length not in", values, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthBetween(Integer value1, Integer value2) {
            addCriterion("min_pwd_length between", value1, value2, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMinPwdLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("min_pwd_length not between", value1, value2, "minPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthIsNull() {
            addCriterion("max_pwd_length is null");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthIsNotNull() {
            addCriterion("max_pwd_length is not null");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthEqualTo(Integer value) {
            addCriterion("max_pwd_length =", value, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthNotEqualTo(Integer value) {
            addCriterion("max_pwd_length <>", value, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthGreaterThan(Integer value) {
            addCriterion("max_pwd_length >", value, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthGreaterThanOrEqualTo(Integer value) {
            addCriterion("max_pwd_length >=", value, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthLessThan(Integer value) {
            addCriterion("max_pwd_length <", value, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthLessThanOrEqualTo(Integer value) {
            addCriterion("max_pwd_length <=", value, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthIn(List<Integer> values) {
            addCriterion("max_pwd_length in", values, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthNotIn(List<Integer> values) {
            addCriterion("max_pwd_length not in", values, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthBetween(Integer value1, Integer value2) {
            addCriterion("max_pwd_length between", value1, value2, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andMaxPwdLengthNotBetween(Integer value1, Integer value2) {
            addCriterion("max_pwd_length not between", value1, value2, "maxPwdLength");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberIsNull() {
            addCriterion("is_contains_number is null");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberIsNotNull() {
            addCriterion("is_contains_number is not null");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberEqualTo(Boolean value) {
            addCriterion("is_contains_number =", value, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberNotEqualTo(Boolean value) {
            addCriterion("is_contains_number <>", value, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberGreaterThan(Boolean value) {
            addCriterion("is_contains_number >", value, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_contains_number >=", value, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberLessThan(Boolean value) {
            addCriterion("is_contains_number <", value, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberLessThanOrEqualTo(Boolean value) {
            addCriterion("is_contains_number <=", value, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberIn(List<Boolean> values) {
            addCriterion("is_contains_number in", values, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberNotIn(List<Boolean> values) {
            addCriterion("is_contains_number not in", values, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberBetween(Boolean value1, Boolean value2) {
            addCriterion("is_contains_number between", value1, value2, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andIsContainsNumberNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_contains_number not between", value1, value2, "isContainsNumber");
            return (Criteria) this;
        }

        public Criteria andValidlyDayIsNull() {
            addCriterion("validly_day is null");
            return (Criteria) this;
        }

        public Criteria andValidlyDayIsNotNull() {
            addCriterion("validly_day is not null");
            return (Criteria) this;
        }

        public Criteria andValidlyDayEqualTo(Integer value) {
            addCriterion("validly_day =", value, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayNotEqualTo(Integer value) {
            addCriterion("validly_day <>", value, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayGreaterThan(Integer value) {
            addCriterion("validly_day >", value, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("validly_day >=", value, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayLessThan(Integer value) {
            addCriterion("validly_day <", value, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayLessThanOrEqualTo(Integer value) {
            addCriterion("validly_day <=", value, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayIn(List<Integer> values) {
            addCriterion("validly_day in", values, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayNotIn(List<Integer> values) {
            addCriterion("validly_day not in", values, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayBetween(Integer value1, Integer value2) {
            addCriterion("validly_day between", value1, value2, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andValidlyDayNotBetween(Integer value1, Integer value2) {
            addCriterion("validly_day not between", value1, value2, "validlyDay");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseIsNull() {
            addCriterion("is_contains_case is null");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseIsNotNull() {
            addCriterion("is_contains_case is not null");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseEqualTo(Boolean value) {
            addCriterion("is_contains_case =", value, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseNotEqualTo(Boolean value) {
            addCriterion("is_contains_case <>", value, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseGreaterThan(Boolean value) {
            addCriterion("is_contains_case >", value, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_contains_case >=", value, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseLessThan(Boolean value) {
            addCriterion("is_contains_case <", value, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseLessThanOrEqualTo(Boolean value) {
            addCriterion("is_contains_case <=", value, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseIn(List<Boolean> values) {
            addCriterion("is_contains_case in", values, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseNotIn(List<Boolean> values) {
            addCriterion("is_contains_case not in", values, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseBetween(Boolean value1, Boolean value2) {
            addCriterion("is_contains_case between", value1, value2, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andIsContainsCaseNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_contains_case not between", value1, value2, "isContainsCase");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumIsNull() {
            addCriterion("pwd_lock_num is null");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumIsNotNull() {
            addCriterion("pwd_lock_num is not null");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumEqualTo(Integer value) {
            addCriterion("pwd_lock_num =", value, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumNotEqualTo(Integer value) {
            addCriterion("pwd_lock_num <>", value, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumGreaterThan(Integer value) {
            addCriterion("pwd_lock_num >", value, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("pwd_lock_num >=", value, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumLessThan(Integer value) {
            addCriterion("pwd_lock_num <", value, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumLessThanOrEqualTo(Integer value) {
            addCriterion("pwd_lock_num <=", value, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumIn(List<Integer> values) {
            addCriterion("pwd_lock_num in", values, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumNotIn(List<Integer> values) {
            addCriterion("pwd_lock_num not in", values, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumBetween(Integer value1, Integer value2) {
            addCriterion("pwd_lock_num between", value1, value2, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andPwdLockNumNotBetween(Integer value1, Integer value2) {
            addCriterion("pwd_lock_num not between", value1, value2, "pwdLockNum");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialIsNull() {
            addCriterion("is_contains_special is null");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialIsNotNull() {
            addCriterion("is_contains_special is not null");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialEqualTo(Boolean value) {
            addCriterion("is_contains_special =", value, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialNotEqualTo(Boolean value) {
            addCriterion("is_contains_special <>", value, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialGreaterThan(Boolean value) {
            addCriterion("is_contains_special >", value, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_contains_special >=", value, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialLessThan(Boolean value) {
            addCriterion("is_contains_special <", value, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialLessThanOrEqualTo(Boolean value) {
            addCriterion("is_contains_special <=", value, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialIn(List<Boolean> values) {
            addCriterion("is_contains_special in", values, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialNotIn(List<Boolean> values) {
            addCriterion("is_contains_special not in", values, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialBetween(Boolean value1, Boolean value2) {
            addCriterion("is_contains_special between", value1, value2, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andIsContainsSpecialNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_contains_special not between", value1, value2, "isContainsSpecial");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumIsNull() {
            addCriterion("non_repeatable_num is null");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumIsNotNull() {
            addCriterion("non_repeatable_num is not null");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumEqualTo(Integer value) {
            addCriterion("non_repeatable_num =", value, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumNotEqualTo(Integer value) {
            addCriterion("non_repeatable_num <>", value, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumGreaterThan(Integer value) {
            addCriterion("non_repeatable_num >", value, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("non_repeatable_num >=", value, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumLessThan(Integer value) {
            addCriterion("non_repeatable_num <", value, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumLessThanOrEqualTo(Integer value) {
            addCriterion("non_repeatable_num <=", value, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumIn(List<Integer> values) {
            addCriterion("non_repeatable_num in", values, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumNotIn(List<Integer> values) {
            addCriterion("non_repeatable_num not in", values, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumBetween(Integer value1, Integer value2) {
            addCriterion("non_repeatable_num between", value1, value2, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andNonRepeatableNumNotBetween(Integer value1, Integer value2) {
            addCriterion("non_repeatable_num not between", value1, value2, "nonRepeatableNum");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeIsNull() {
            addCriterion("expires_time is null");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeIsNotNull() {
            addCriterion("expires_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeEqualTo(Integer value) {
            addCriterion("expires_time =", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeNotEqualTo(Integer value) {
            addCriterion("expires_time <>", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeGreaterThan(Integer value) {
            addCriterion("expires_time >", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("expires_time >=", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeLessThan(Integer value) {
            addCriterion("expires_time <", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeLessThanOrEqualTo(Integer value) {
            addCriterion("expires_time <=", value, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeIn(List<Integer> values) {
            addCriterion("expires_time in", values, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeNotIn(List<Integer> values) {
            addCriterion("expires_time not in", values, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeBetween(Integer value1, Integer value2) {
            addCriterion("expires_time between", value1, value2, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andExpiresTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("expires_time not between", value1, value2, "expiresTime");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditIsNull() {
            addCriterion("is_open_credit is null");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditIsNotNull() {
            addCriterion("is_open_credit is not null");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditEqualTo(Boolean value) {
            addCriterion("is_open_credit =", value, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditNotEqualTo(Boolean value) {
            addCriterion("is_open_credit <>", value, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditGreaterThan(Boolean value) {
            addCriterion("is_open_credit >", value, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_open_credit >=", value, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditLessThan(Boolean value) {
            addCriterion("is_open_credit <", value, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditLessThanOrEqualTo(Boolean value) {
            addCriterion("is_open_credit <=", value, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditIn(List<Boolean> values) {
            addCriterion("is_open_credit in", values, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditNotIn(List<Boolean> values) {
            addCriterion("is_open_credit not in", values, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditBetween(Boolean value1, Boolean value2) {
            addCriterion("is_open_credit between", value1, value2, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andIsOpenCreditNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_open_credit not between", value1, value2, "isOpenCredit");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}