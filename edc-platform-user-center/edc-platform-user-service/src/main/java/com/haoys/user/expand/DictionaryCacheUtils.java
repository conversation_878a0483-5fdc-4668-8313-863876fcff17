package com.haoys.user.expand;

import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.model.Dictionary;

import java.util.List;

/**
 * 字典工具类
 */
public class DictionaryCacheUtils {

    /**
     * 设置字典缓存
     * @param key 参数键
     * @param dictDatas 字典数据列表
     */
    public static void setDictCache(String key, List<Dictionary> dictDatas) {
        SpringUtils.getBean(RedisTemplateService.class).set(getCacheKey(key), dictDatas);
    }


    /**
     * 设置字典缓存
     *
     * @param key 参数键
     * @param dictionary 字典数据
     */
    public static void setDict(String key, Dictionary dictionary) {
        SpringUtils.getBean(RedisTemplateService.class).set(getCacheKey(key), dictionary);
    }



    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static List<Dictionary> getDictCache(String key) {
        Object cacheObj = SpringUtils.getBean(RedisTemplateService.class).get(getCacheKey(key));
        if (StringUtils.isNotNull(cacheObj)) {
            return StringUtils.cast(cacheObj);
        }
        return null;
    }



    /**
     * 获取字典缓存
     *
     * @param key 参数键
     * @return dictDatas 字典数据列表
     */
    public static Dictionary getDict(String key) {
        Object cacheObj = SpringUtils.getBean(RedisTemplateService.class).get(getCacheKey(key));
        if (StringUtils.isNotNull(cacheObj)) {
            return StringUtils.cast(cacheObj);
        }
        return null;
    }

    /**
     * 删除指定字典缓存
     *
     * @param key 字典键
     */
    public static void removeDictCache(String key) {
        SpringUtils.getBean(RedisTemplateService.class).del(getCacheKey(key));
    }


    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    public static String getCacheKey(String configKey) {
        return Constants.SYS_DICT_KEY + configKey;
    }
}
