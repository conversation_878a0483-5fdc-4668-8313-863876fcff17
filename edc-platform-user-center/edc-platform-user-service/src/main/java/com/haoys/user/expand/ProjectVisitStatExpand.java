package com.haoys.user.expand;

import com.haoys.user.common.api.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectVisitStatExpand {
    
    public DynamicColumnHeadRowConfig DynamicColumnHeadRowConfig;
    public FixedColumnHeadRowConfig FixedColumnHeadRowConfig;
    
    @ApiModelProperty(value = "访视动态列名称")
    private List<ProjectVisitStatExpand.DynamicColumnHeadRowConfig> visitHeadRowList = new ArrayList<>();
    
    @ApiModelProperty(value = "访视统计列表")
    private CommonPage<ProjectUserExpand> projectVisitDataList = new CommonPage<>();
    
    @Data
    public static class DynamicColumnHeadRowConfig{
        private FixedColumnHeadRowConfig fixedColumnHeadRowConfig;
        private OneLevelHeadRowConfig oneLevelHeadRowConfig;
    }
    
    @Data
    public static class FixedColumnHeadRowConfig{
        private String userName;
        private String realName;
        private String area;
        private String group;
        private String status;
    }
    
    @Data
    public static class OneLevelHeadRowConfig{
        private String visitId;
        private String visitName;
        public List<ChildLevelHeadRowConfig> childLevelHeadRowConfigList = new ArrayList<>();
    }
    
    @Data
    public static class ChildLevelHeadRowConfig{
        private String name;
    }

}
