package com.haoys.user.mapper;

import com.haoys.user.model.SystemTenant;
import com.haoys.user.model.SystemTenantExample;
import java.util.List;

import com.haoys.user.model.SystemUserInfo;
import org.apache.ibatis.annotations.Param;

public interface SystemTenantMapper {
    long countByExample(SystemTenantExample example);

    int deleteByExample(SystemTenantExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemTenant record);

    int insertSelective(SystemTenant record);

    List<SystemTenant> selectByExampleWithBLOBs(SystemTenantExample example);

    List<SystemTenant> selectByExample(SystemTenantExample example);

    SystemTenant selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemTenant record, @Param("example") SystemTenantExample example);

    int updateByExampleWithBLOBs(@Param("record") SystemTenant record, @Param("example") SystemTenantExample example);

    int updateByExample(@Param("record") SystemTenant record, @Param("example") SystemTenantExample example);

    int updateByPrimaryKeySelective(SystemTenant record);

    int updateByPrimaryKeyWithBLOBs(SystemTenant record);

    int updateByPrimaryKey(SystemTenant record);
    
    List<SystemUserInfo> selectTenantUserByEnterprise(Long enterprise);
}