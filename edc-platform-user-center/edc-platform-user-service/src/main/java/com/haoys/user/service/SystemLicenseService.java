package com.haoys.user.service;

import com.haoys.user.common.domain.SystemLicenseInfo;

import java.util.Date;
import java.util.List;

/**
 * 系统许可证服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
public interface SystemLicenseService {
    
    /**
     * 初始化系统许可证
     */
    void initializeSystemLicense();
    
    /**
     * 验证系统许可证
     */
    boolean validateSystemLicense();
    
    /**
     * 检查系统是否可用
     */
    boolean isSystemAvailable();

    /**
     * 获取证书限制配置状态
     */
    boolean isCertificateRestrictionEnabled();

    /**
     * 获取系统限制信息
     */
    String getSystemRestrictionMessage();
    
    /**
     * 获取当前有效的许可证
     */
    SystemLicenseInfo getCurrentLicense();
    
    /**
     * 更新许可证到期时间
     */
    boolean updateLicenseExpireTime(Date newExpireTime, String accessToken);
    
    /**
     * 刷新许可证状态
     */
    boolean refreshLicenseStatus(String accessToken);
    
    /**
     * 获取所有许可证信息
     */
    List<SystemLicenseInfo> getAllLicenses();
    
    /**
     * 创建新的许可证
     */
    SystemLicenseInfo createLicense(String licenseName, String licenseType, Date expireTime, 
                                   Integer maxUsers, Integer maxProjects, String featurePermissions);
    
    /**
     * 激活许可证
     */
    boolean activateLicense(Long licenseId, String accessToken);
    
    /**
     * 禁用许可证
     */
    boolean disableLicense(Long licenseId, String accessToken);
    
    /**
     * 删除许可证
     */
    boolean deleteLicense(Long licenseId, String accessToken);
    
    /**
     * 验证访问令牌
     */
    boolean validateAccessToken(String accessToken);
    
    /**
     * 获取许可证详细信息
     */
    SystemLicenseInfo getLicenseById(Long licenseId);
    
    /**
     * 检查用户数量限制
     */
    boolean checkUserLimit();
    
    /**
     * 检查项目数量限制
     */
    boolean checkProjectLimit();
    
    /**
     * 获取剩余天数
     */
    long getRemainingDays();
    
    /**
     * 生成新的密钥对
     */
    void generateNewKeyPair(Long licenseId, String accessToken);
}
