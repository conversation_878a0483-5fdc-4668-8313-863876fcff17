package com.haoys.user.domain.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SystemDictionaryParam {

    @ApiModelProperty(value = "字典id")
    private Long id;

    @ApiModelProperty(value = "字典名称")
    private String name;

    @ApiModelProperty(value = "字典英文名称")
    private String enName;

    @ApiModelProperty(value = "父级id-字典类型设置0，字典明细设置对应分类id")
    private String parentId;

    @ApiModelProperty(value = "字典值")
    private String value;

    @ApiModelProperty(value = "字典编码")
    private String code;
    
    @ApiModelProperty(value = "分值")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "状态值 0-启用 1-停用")
    private String status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "描述")
    private String description;

}
