package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class ProjectResearchersInfo implements Serializable {
    
    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "组")
    private String groupInfo;

    @ApiModelProperty(value = "医院")
    private String hospital;

    @ApiModelProperty(value = "科室")
    private String dept;

    @ApiModelProperty(value = "开户行")
    private String openBank;

    @ApiModelProperty(value = "身份证")
    private String identityCard;

    @ApiModelProperty(value = "手机号")
    private String telPhone;

    @ApiModelProperty(value = "人身像地址")
    private String idCardFacePhoto;

    @ApiModelProperty(value = "国徽像地址")
    private String idCardBackPhoto;

    @ApiModelProperty(value = "银行账号")
    private String bankNumber;

    @ApiModelProperty(value = "项目志愿者手机号")
    private String projectVolunteerPhone;

    @ApiModelProperty(value = "项目志愿者")
    private String projectVolunteer;

    @ApiModelProperty(value = "资格证")
    private String certificate;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getGroupInfo() {
        return groupInfo;
    }

    public void setGroupInfo(String groupInfo) {
        this.groupInfo = groupInfo;
    }

    public String getHospital() {
        return hospital;
    }

    public void setHospital(String hospital) {
        this.hospital = hospital;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getOpenBank() {
        return openBank;
    }

    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    public String getIdentityCard() {
        return identityCard;
    }

    public void setIdentityCard(String identityCard) {
        this.identityCard = identityCard;
    }

    public String getTelPhone() {
        return telPhone;
    }

    public void setTelPhone(String telPhone) {
        this.telPhone = telPhone;
    }

    public String getIdCardFacePhoto() {
        return idCardFacePhoto;
    }

    public void setIdCardFacePhoto(String idCardFacePhoto) {
        this.idCardFacePhoto = idCardFacePhoto;
    }

    public String getIdCardBackPhoto() {
        return idCardBackPhoto;
    }

    public void setIdCardBackPhoto(String idCardBackPhoto) {
        this.idCardBackPhoto = idCardBackPhoto;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getProjectVolunteerPhone() {
        return projectVolunteerPhone;
    }

    public void setProjectVolunteerPhone(String projectVolunteerPhone) {
        this.projectVolunteerPhone = projectVolunteerPhone;
    }

    public String getProjectVolunteer() {
        return projectVolunteer;
    }

    public void setProjectVolunteer(String projectVolunteer) {
        this.projectVolunteer = projectVolunteer;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", name=").append(name);
        sb.append(", area=").append(area);
        sb.append(", groupInfo=").append(groupInfo);
        sb.append(", hospital=").append(hospital);
        sb.append(", dept=").append(dept);
        sb.append(", openBank=").append(openBank);
        sb.append(", identityCard=").append(identityCard);
        sb.append(", telPhone=").append(telPhone);
        sb.append(", idCardFacePhoto=").append(idCardFacePhoto);
        sb.append(", idCardBackPhoto=").append(idCardBackPhoto);
        sb.append(", bankNumber=").append(bankNumber);
        sb.append(", projectVolunteerPhone=").append(projectVolunteerPhone);
        sb.append(", projectVolunteer=").append(projectVolunteer);
        sb.append(", certificate=").append(certificate);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}