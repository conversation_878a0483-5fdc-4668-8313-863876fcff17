package com.haoys.user.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统访问日志记录实体类
 * 用于医学审计日志，禁止删除
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@ApiModel(value = "SystemRequestRecord", description = "系统访问日志记录")
public class SystemRequestRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    
    @ApiModelProperty(value = "用户账号")
    private String userName;
    
    @ApiModelProperty(value = "用户真实姓名")
    private String realName;
    
    @ApiModelProperty(value = "会话ID")
    private String sessionId;
    
    @ApiModelProperty(value = "链路追踪ID")
    private String traceId;
    
    // 请求信息
    @ApiModelProperty(value = "请求名称/模块标题")
    private String requestName;
    
    @ApiModelProperty(value = "请求方法(GET/POST/PUT/DELETE)")
    private String requestMethod;
    
    @ApiModelProperty(value = "请求URL")
    private String requestUrl;
    
    @ApiModelProperty(value = "调用方法名")
    private String methodName;
    
    @ApiModelProperty(value = "请求参数")
    private String requestParam;
    
    @ApiModelProperty(value = "请求开始时间")
    private Date requestStartTime;
    
    @ApiModelProperty(value = "请求结束时间")
    private Date requestEndTime;
    
    // 响应信息
    @ApiModelProperty(value = "响应结果")
    private String responseResult;
    
    @ApiModelProperty(value = "响应时间(毫秒)")
    private Long responseTime;
    
    @ApiModelProperty(value = "响应大小(字节)")
    private Long responseSize;
    
    @ApiModelProperty(value = "HTTP状态码")
    private Integer httpStatus;
    
    @ApiModelProperty(value = "是否成功(0失败 1成功)")
    private Boolean isSuccess;
    
    // 网络信息
    @ApiModelProperty(value = "请求IP地址")
    private String requestIp;
    
    @ApiModelProperty(value = "IP地理位置")
    private String location;
    
    @ApiModelProperty(value = "用户代理")
    private String userAgent;
    
    @ApiModelProperty(value = "来源页面")
    private String referer;
    
    // 设备信息
    @ApiModelProperty(value = "浏览器类型")
    private String browser;
    
    @ApiModelProperty(value = "操作系统")
    private String operatingSystem;
    
    @ApiModelProperty(value = "设备类型(DESKTOP/MOBILE/TABLET/UNKNOWN)")
    private String deviceType;
    
    // 业务信息
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    
    @ApiModelProperty(value = "操作类型")
    private String operatorType;
    
    @ApiModelProperty(value = "数据来源平台")
    private String dataFrom;
    
    @ApiModelProperty(value = "访问类型(WEB/API/MOBILE)")
    private String accessType;
    
    @ApiModelProperty(value = "是否项目日志(0否 1是)")
    private Boolean projectRecordLog;
    
    // 异常信息
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;
    
    @ApiModelProperty(value = "异常类型")
    private String exceptionType;
    
    @ApiModelProperty(value = "异常堆栈(简化)")
    private String exceptionStack;
    
    // 系统信息
    @ApiModelProperty(value = "操作状态(0异常 1正常)")
    private Integer status;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    
    // 构造函数
    public SystemRequestRecord() {
        this.createTime = new Date();
        this.isSuccess = true;
        this.status = 1;
        this.deviceType = "UNKNOWN";
        this.accessType = "WEB";
        this.projectRecordLog = false;
    }
    
    // Getter and Setter methods
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getTraceId() {
        return traceId;
    }
    
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
    
    public String getRequestName() {
        return requestName;
    }
    
    public void setRequestName(String requestName) {
        this.requestName = requestName;
    }
    
    public String getRequestMethod() {
        return requestMethod;
    }
    
    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }
    
    public String getRequestUrl() {
        return requestUrl;
    }
    
    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }
    
    public String getMethodName() {
        return methodName;
    }
    
    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
    
    public String getRequestParam() {
        return requestParam;
    }
    
    public void setRequestParam(String requestParam) {
        this.requestParam = requestParam;
    }
    
    public Date getRequestStartTime() {
        return requestStartTime;
    }
    
    public void setRequestStartTime(Date requestStartTime) {
        this.requestStartTime = requestStartTime;
    }
    
    public Date getRequestEndTime() {
        return requestEndTime;
    }
    
    public void setRequestEndTime(Date requestEndTime) {
        this.requestEndTime = requestEndTime;
    }
    
    public String getResponseResult() {
        return responseResult;
    }
    
    public void setResponseResult(String responseResult) {
        this.responseResult = responseResult;
    }
    
    public Long getResponseTime() {
        return responseTime;
    }
    
    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }
    
    public Long getResponseSize() {
        return responseSize;
    }
    
    public void setResponseSize(Long responseSize) {
        this.responseSize = responseSize;
    }
    
    public Integer getHttpStatus() {
        return httpStatus;
    }
    
    public void setHttpStatus(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }
    
    public Boolean getIsSuccess() {
        return isSuccess;
    }
    
    public void setIsSuccess(Boolean isSuccess) {
        this.isSuccess = isSuccess;
    }
    
    public String getRequestIp() {
        return requestIp;
    }
    
    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getReferer() {
        return referer;
    }
    
    public void setReferer(String referer) {
        this.referer = referer;
    }
    
    public String getBrowser() {
        return browser;
    }
    
    public void setBrowser(String browser) {
        this.browser = browser;
    }
    
    public String getOperatingSystem() {
        return operatingSystem;
    }
    
    public void setOperatingSystem(String operatingSystem) {
        this.operatingSystem = operatingSystem;
    }
    
    public String getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public String getOperatorType() {
        return operatorType;
    }
    
    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }
    
    public String getDataFrom() {
        return dataFrom;
    }
    
    public void setDataFrom(String dataFrom) {
        this.dataFrom = dataFrom;
    }
    
    public String getAccessType() {
        return accessType;
    }
    
    public void setAccessType(String accessType) {
        this.accessType = accessType;
    }
    
    public Boolean getProjectRecordLog() {
        return projectRecordLog;
    }
    
    public void setProjectRecordLog(Boolean projectRecordLog) {
        this.projectRecordLog = projectRecordLog;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getExceptionType() {
        return exceptionType;
    }
    
    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }
    
    public String getExceptionStack() {
        return exceptionStack;
    }
    
    public void setExceptionStack(String exceptionStack) {
        this.exceptionStack = exceptionStack;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "SystemRequestRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", realName='" + realName + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", traceId='" + traceId + '\'' +
                ", requestName='" + requestName + '\'' +
                ", requestMethod='" + requestMethod + '\'' +
                ", requestUrl='" + requestUrl + '\'' +
                ", methodName='" + methodName + '\'' +
                ", responseTime=" + responseTime +
                ", httpStatus=" + httpStatus +
                ", isSuccess=" + isSuccess +
                ", requestIp='" + requestIp + '\'' +
                ", location='" + location + '\'' +
                ", browser='" + browser + '\'' +
                ", operatingSystem='" + operatingSystem + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", businessType='" + businessType + '\'' +
                ", operatorType='" + operatorType + '\'' +
                ", dataFrom='" + dataFrom + '\'' +
                ", accessType='" + accessType + '\'' +
                ", projectRecordLog=" + projectRecordLog +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}
