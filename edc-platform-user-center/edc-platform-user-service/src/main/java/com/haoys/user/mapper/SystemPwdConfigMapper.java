package com.haoys.user.mapper;

import com.haoys.user.model.SystemPwdConfig;
import com.haoys.user.model.SystemPwdConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemPwdConfigMapper {
    long countByExample(SystemPwdConfigExample example);

    int deleteByExample(SystemPwdConfigExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SystemPwdConfig record);

    int insertSelective(SystemPwdConfig record);

    List<SystemPwdConfig> selectByExample(SystemPwdConfigExample example);

    SystemPwdConfig selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SystemPwdConfig record, @Param("example") SystemPwdConfigExample example);

    int updateByExample(@Param("record") SystemPwdConfig record, @Param("example") SystemPwdConfigExample example);

    int updateByPrimaryKeySelective(SystemPwdConfig record);

    int updateByPrimaryKey(SystemPwdConfig record);
}
