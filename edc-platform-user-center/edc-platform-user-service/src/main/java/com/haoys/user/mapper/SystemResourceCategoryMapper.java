package com.haoys.user.mapper;

import com.haoys.user.model.SystemResourceCategory;
import com.haoys.user.model.SystemResourceCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemResourceCategoryMapper {
    long countByExample(SystemResourceCategoryExample example);

    int deleteByExample(SystemResourceCategoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemResourceCategory record);

    int insertSelective(SystemResourceCategory record);

    List<SystemResourceCategory> selectByExample(SystemResourceCategoryExample example);

    SystemResourceCategory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemResourceCategory record, @Param("example") SystemResourceCategoryExample example);

    int updateByExample(@Param("record") SystemResourceCategory record, @Param("example") SystemResourceCategoryExample example);

    int updateByPrimaryKeySelective(SystemResourceCategory record);

    int updateByPrimaryKey(SystemResourceCategory record);
}