package com.haoys.user.domain.vo.rcts;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/5 14:31
 */
@Data
public class RandomizedParamsVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "入组上限")
    private Integer joinGroupLimit = 1000;

    @ApiModelProperty(value = "变量名称")
    private String randomizedType;

    @ApiModelProperty(value = "变量code")
    private String randomizedMethod;

    @ApiModelProperty(value = "随机号规则")
    private String randomizedRule;

    @ApiModelProperty(value = "区组长度")
    private Integer groupSize = 1;

    @ApiModelProperty(value = "随机号前缀")
    private String randomizedFrefix;

    @ApiModelProperty(value = "随机号位数")
    private Integer randomizedDigit;

    @ApiModelProperty(value = "内嵌中心code")
    private Boolean concatOrgCode;

    @ApiModelProperty(value = "随机种子")
    private Integer randomizedSeed = 1024;

    @ApiModelProperty(value = "随机分组-json格式")
    private String randomizedGroupConfig;

    @ApiModelProperty(value = "分层因素-json格式")
    private String randomizedLayerConfig;

    @ApiModelProperty(value = "启用-1 停用-0")
    private Boolean enabled;

}
