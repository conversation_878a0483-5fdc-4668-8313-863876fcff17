package com.haoys.user.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SystemUserInfo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "用户头像")
    private String icon;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户注册来源")
    private String userType;

    @ApiModelProperty(value = "用户姓名")
    private String realName;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;

    @ApiModelProperty(value = "预留第三方登录对接id")
    private String thirdOpenId;

    @ApiModelProperty(value = "bdp用户id")
    private String bpdUserId;

    @ApiModelProperty(value = "备注信息")
    private String note;

    @ApiModelProperty(value = "系统角色描述信息-PM等")
    private String roleDesc;

    @ApiModelProperty(value = "token信息")
    private String tokenValue;

    @ApiModelProperty(value = "token过期时间")
    private Date expireTime;

    @ApiModelProperty(value = "登录code")
    private String loginCode;

    @ApiModelProperty(value = "数据状态 0/1")
    private Boolean sealFlag;

    @ApiModelProperty(value = "启用状态0/1")
    private Integer status;

    @ApiModelProperty(value = "激活状态0/1")
    private Boolean activeStatus;

    @ApiModelProperty(value = "锁定状态0/1")
    private Boolean lockStatus;

    @ApiModelProperty(value = "工作单位-参照系统中心id")
    private String department;

    @ApiModelProperty(value = "职称-参照系统字典")
    private String positional;

    @ApiModelProperty(value = "企业部门")
    private String enterprise;

    @ApiModelProperty(value = "工作地址")
    private String address;

    @ApiModelProperty(value = "创建项目数量限制")
    private Integer projectCountLimit;

    @ApiModelProperty(value = "项目新增参与者限制")
    private Integer projectTesteeLimit;

    @ApiModelProperty(value = "是否为运营方项目管理员")
    private Boolean companyOwnerUser;

    @ApiModelProperty(value = "默认企业管理员标识")
    private Boolean defaultAdmin;

    @ApiModelProperty(value = "身份类型 1-项目负责人 2-医生 3-CRC 4-数据经理 5-医学经理")
    private String identityType;

    @ApiModelProperty(value = "注册来源")
    private String registerFrom;

    @ApiModelProperty(value = "登录设备")
    private String loginDevice;

    @ApiModelProperty(value = "所属管理员-用户产品推广")
    private Long parentUser;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "最后修改密码时间")
    private Date lastModifyPwdTime;

    @ApiModelProperty(value = "最新登录时间")
    private Date loginTime;

    private static final long serialVersionUID = 1L;


}
