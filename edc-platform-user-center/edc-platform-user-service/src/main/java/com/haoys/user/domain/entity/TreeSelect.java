package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.haoys.user.common.core.domain.entity.BaseSystemArea;
import com.haoys.user.model.SystemMenu;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 */
public class TreeSelect implements Serializable {

    @ApiModelProperty(value = "节点id")
    private Long id;

    @ApiModelProperty(value = "节点名称")
    private String label;

    @ApiModelProperty(value = "是否为系统默认选中菜单 1-true 0-false")
    private Boolean systemDefault;

    @ApiModelProperty(value = "项目定制角色设置是否显示，CRA不显示新增参与者")
    private String customRoleHidden;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    public TreeSelect(SystemMenu menu) {
        this.id = menu.getId();
        this.label = menu.getTitle();
        this.systemDefault = menu.getSystemDefault();
        this.customRoleHidden = menu.getCustomRoleHidden();
        this.children = menu.getChildrens().stream().map(TreeSelect::new).collect(Collectors.toList());
    }
    public TreeSelect(BaseSystemArea baseSystemArea) {
        this.id = baseSystemArea.getId();
        this.label = baseSystemArea.getCityName();
        this.children = baseSystemArea.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<TreeSelect> getChildren() {
        return children;
    }

    public void setChildren(List<TreeSelect> children) {
        this.children = children;
    }

    public Boolean getSystemDefault() {
        return systemDefault;
    }

    public void setSystemDefault(Boolean systemDefault) {
        this.systemDefault = systemDefault;
    }

    public String getCustomRoleHidden() {
        return customRoleHidden;
    }

    public void setCustomRoleHidden(String customRoleHidden) {
        this.customRoleHidden = customRoleHidden;
    }
}
