package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class SystemPwdConfig implements Serializable {
    private Integer id;

    @ApiModelProperty(value = "最小密码长度（位）")
    private Integer minPwdLength;

    @ApiModelProperty(value = "最大密码长度（位）")
    private Integer maxPwdLength;

    @ApiModelProperty(value = "密码包含数字 0否 1是")
    private Boolean isContainsNumber;

    @ApiModelProperty(value = "密码有效期（天）")
    private Integer validlyDay;

    @ApiModelProperty(value = "密码包含大小写0否 1是")
    private Boolean isContainsCase;

    @ApiModelProperty(value = "密码连续错误次数锁定（次）")
    private Integer pwdLockNum;

    @ApiModelProperty(value = "密码包含特殊字符0否 1是")
    private Boolean isContainsSpecial;

    @ApiModelProperty(value = "密码历史不可重复（次）")
    private Integer nonRepeatableNum;

    @ApiModelProperty(value = "长时间不操作登出（分钟）")
    private Integer expiresTime;

    @ApiModelProperty(value = "是否开启组织机构云词典(信用中国)")
    private Boolean isOpenCredit;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMinPwdLength() {
        return minPwdLength;
    }

    public void setMinPwdLength(Integer minPwdLength) {
        this.minPwdLength = minPwdLength;
    }

    public Integer getMaxPwdLength() {
        return maxPwdLength;
    }

    public void setMaxPwdLength(Integer maxPwdLength) {
        this.maxPwdLength = maxPwdLength;
    }

    public Boolean getIsContainsNumber() {
        return isContainsNumber;
    }

    public void setIsContainsNumber(Boolean isContainsNumber) {
        this.isContainsNumber = isContainsNumber;
    }

    public Integer getValidlyDay() {
        return validlyDay;
    }

    public void setValidlyDay(Integer validlyDay) {
        this.validlyDay = validlyDay;
    }

    public Boolean getIsContainsCase() {
        return isContainsCase;
    }

    public void setIsContainsCase(Boolean isContainsCase) {
        this.isContainsCase = isContainsCase;
    }

    public Integer getPwdLockNum() {
        return pwdLockNum;
    }

    public void setPwdLockNum(Integer pwdLockNum) {
        this.pwdLockNum = pwdLockNum;
    }

    public Boolean getIsContainsSpecial() {
        return isContainsSpecial;
    }

    public void setIsContainsSpecial(Boolean isContainsSpecial) {
        this.isContainsSpecial = isContainsSpecial;
    }

    public Integer getNonRepeatableNum() {
        return nonRepeatableNum;
    }

    public void setNonRepeatableNum(Integer nonRepeatableNum) {
        this.nonRepeatableNum = nonRepeatableNum;
    }

    public Integer getExpiresTime() {
        return expiresTime;
    }

    public void setExpiresTime(Integer expiresTime) {
        this.expiresTime = expiresTime;
    }

    public Boolean getIsOpenCredit() {
        return isOpenCredit;
    }

    public void setIsOpenCredit(Boolean isOpenCredit) {
        this.isOpenCredit = isOpenCredit;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", minPwdLength=").append(minPwdLength);
        sb.append(", maxPwdLength=").append(maxPwdLength);
        sb.append(", isContainsNumber=").append(isContainsNumber);
        sb.append(", validlyDay=").append(validlyDay);
        sb.append(", isContainsCase=").append(isContainsCase);
        sb.append(", pwdLockNum=").append(pwdLockNum);
        sb.append(", isContainsSpecial=").append(isContainsSpecial);
        sb.append(", nonRepeatableNum=").append(nonRepeatableNum);
        sb.append(", expiresTime=").append(expiresTime);
        sb.append(", isOpenCredit=").append(isOpenCredit);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}