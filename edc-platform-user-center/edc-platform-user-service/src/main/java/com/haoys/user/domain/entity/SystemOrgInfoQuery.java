package com.haoys.user.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.common.annotation.Excel;
import com.haoys.user.common.core.domain.vo.BaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class SystemOrgInfoQuery extends BaseVo implements Serializable  {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long orgId;

    @Excel(name = "中心名称")
    private String orgName;

    private String aName1;

    private String aName2;

    private String contact;

    private String tel;

    private String address;

    private String zip;

    private String passwd;

    private Date updateTime;

    private String operator;

    private String status;

    @Excel(name = "医院等级")
    private String orgType;

    private Date createTime;

    private String regionCode;

    private String orgCode;

    private Integer isAuth;

    @ApiModelProperty(value = "省code")
    private Long provinceCode;

    @ApiModelProperty(value = "市code")
    private Long cityCode;

    @ApiModelProperty(value = "县code")
    private Long countyCode;

    private String city;

    private String orgLevel;

    @Excel(name = "省")
    private String provinceName;

    @Excel(name = "市")
    private String cityName;

    @Excel(name = "县（区）")
    private String countyName;

    private String value;

    @ApiModelProperty(value = "同步信用中国机构信息")
    private Boolean updateSyncSystemOrg = false;

}