package com.haoys.user.mapper;

import com.haoys.user.model.DictionaryFrom;
import com.haoys.user.model.DictionaryFromExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DictionaryFromMapper {
    long countByExample(DictionaryFromExample example);

    int deleteByExample(DictionaryFromExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DictionaryFrom record);

    int insertSelective(DictionaryFrom record);

    List<DictionaryFrom> selectByExample(DictionaryFromExample example);

    DictionaryFrom selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DictionaryFrom record, @Param("example") DictionaryFromExample example);

    int updateByExample(@Param("record") DictionaryFrom record, @Param("example") DictionaryFromExample example);

    int updateByPrimaryKeySelective(DictionaryFrom record);

    int updateByPrimaryKey(DictionaryFrom record);
}