package com.haoys.user.junit;

import com.haoys.user.common.util.SnowflakeIdWorker;
import org.apache.commons.lang.RandomStringUtils;

import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class DorisJDBCDemo {

    private static final String JDBC_DRIVER = "com.mysql.cj.jdbc.Driver";
    private static final String DB_URL_PATTERN = "***************************************************";
    private static final String HOST = "**************"; // Leader Node host
    private static final int PORT = 9030;   // query_port of Leader Node
    private static final String DB = "disease_data_center";
    private static final String TBL = "testee_variable_result";
    private static final String USER = "root";
    private static final String PASSWD = "";

    private static final int INSERT_BATCH_SIZE = 10000;

    public static void main(String[] args) {
        for (int i = 0; i < 200; i++) {
            insert();
        }
    }

    private static void insert() {
        // 注意末尾不要加 分号 ";"
        String query = "insert into " + TBL + " values(?, ?, ?, ?, ?, ?, ?, ?, ?)";
        // 设置 Label 以做到幂等。
        // String query = "insert into " + TBL + " WITH LABEL my_label values(?, ?)";

        Connection conn = null;
        PreparedStatement stmt = null;
        String dbUrl = String.format(DB_URL_PATTERN, HOST, PORT, DB);
        try {
            Class.forName(JDBC_DRIVER);
            conn = DriverManager.getConnection(dbUrl, USER, PASSWD);
            stmt = conn.prepareStatement(query);

            // project_id,plan_id,visit_id,form_id,form_detail_id,testee_id,field_name,field_value,create_time
            for (int i =0; i < INSERT_BATCH_SIZE; i++) {
                stmt.setLong(1, SnowflakeIdWorker.getUuid());
                stmt.setLong(2, SnowflakeIdWorker.getUuid());
                stmt.setLong(3, SnowflakeIdWorker.getUuid());
                stmt.setLong(4, SnowflakeIdWorker.getUuid());
                stmt.setLong(5, SnowflakeIdWorker.getUuid());
                stmt.setLong(6, SnowflakeIdWorker.getUuid());
                stmt.setString(7, RandomStringUtils.randomAlphanumeric(22));
                stmt.setString(8,RandomStringUtils.randomAlphanumeric(16));
                stmt.setDate(9, new Date(System.currentTimeMillis()));
                stmt.addBatch();
            }

            int[] res = stmt.executeBatch();
            System.out.println(res);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (stmt != null) {
                    stmt.close();
                }
            } catch (SQLException se2) {
                se2.printStackTrace();
            }
            try {
                if (conn != null) {
                    conn.close();
                }
            } catch (SQLException se) {
                se.printStackTrace();
            }
        }
    }
}
