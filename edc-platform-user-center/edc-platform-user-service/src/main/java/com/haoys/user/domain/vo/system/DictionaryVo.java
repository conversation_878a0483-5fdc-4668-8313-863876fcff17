package com.haoys.user.domain.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class DictionaryVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "字典id")
    private Long id;

    @ApiModelProperty(value = "字典名称")
    private String name;

    @ApiModelProperty(value = "英文名称")
    private String enName;

    @ApiModelProperty(value = "父级ID")
    private String parentId;

    @ApiModelProperty(value = "字典值")
    private String value;

    @ApiModelProperty(value = "code码")
    private String code;
    
    @ApiModelProperty(value = "分值")
    private BigDecimal scoreValue;

    @ApiModelProperty(value = "是否系统默认-禁止修改")
    private Boolean systemDefault;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态 0-正常 1-无效")
    private String status;

    @ApiModelProperty(value = "字典值数据集合")
    private List<DictionaryVo> childList;
}
