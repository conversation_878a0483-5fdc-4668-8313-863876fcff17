#!/bin/bash

# =============================================================================
# LocalOfficeModel - 文件组织规范检查脚本
# 功能：检查项目文件是否按照规范正确分类存放（本地办公模式）
# 作者：EDC Research Team
# 创建时间：$(date +%Y-%m-%d)
# 模式：本地办公模式 (LocalOfficeModel)
# =============================================================================

echo "🔍 [LocalOfficeModel] 开始检查文件组织规范..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
VIOLATIONS=0
WARNINGS=0
SUGGESTIONS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[LocalOfficeModel-INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[LocalOfficeModel-SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[LocalOfficeModel-WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[LocalOfficeModel-ERROR]${NC} $1"
}

echo ""
echo "🚨 检查根目录文件管控..."

# 定义允许在根目录存在的文件（本地办公模式更严格）
ALLOWED_ROOT_FILES=("README.md" "pom.xml" "research-master.iml" ".gitignore")

# 检查根目录是否有不应该存在的文件
log_info "检查项目根目录文件规范..."
for file in *.md *.yml *.yaml *.conf *.cnf *.html *.sh *.sql *.java *.xml *.txt *.log; do
    if [ -f "$file" ]; then
        # 检查是否在允许列表中
        allowed=false
        for allowed_file in "${ALLOWED_ROOT_FILES[@]}"; do
            if [ "$file" = "$allowed_file" ]; then
                allowed=true
                break
            fi
        done

        if [ "$allowed" = false ]; then
            log_error "违规: 根目录发现不允许的文件: $file"
            echo "  建议移动到: $(suggest_file_location "$file")"
            VIOLATIONS=$((VIOLATIONS + 1))
        fi
    fi
done

# 建议文件位置的函数
suggest_file_location() {
    local file="$1"
    case "$file" in
        *report*.md|*fix*.md|*analysis*.md|*summary*.md)
            echo "docs/reports/"
            ;;
        *guide*.md|*manual*.md|*training*.md)
            echo "docs/guides/"
            ;;
        *architecture*.md|*design*.md|system-*.md)
            echo "docs/architecture/"
            ;;
        *deployment*.md|*deploy*.md)
            echo "docs/deployment/"
            ;;
        git-*.md|*git*.md)
            echo "docs/git/"
            ;;
        *.sql|create_*.sql|alter_*.sql)
            echo "scripts/database/"
            ;;
        *.sh|*.bat)
            echo "scripts/automation/ 或其他适当的scripts子目录"
            ;;
        *.yml|*.yaml|*.conf)
            echo "相应的配置目录或scripts/deployment/"
            ;;
        *)
            echo "适当的分类目录"
            ;;
    esac
}

# 建议脚本位置的函数
suggest_script_location() {
    local file="$1"
    case "$file" in
        *test*.sh|*verify*.sh|*.html)
            echo "scripts/testing/"
            ;;
        *deploy*.sh|*deploy*.bat|*.conf)
            echo "scripts/deployment/"
            ;;
        *.sql|*database*.sh)
            echo "scripts/database/"
            ;;
        *performance*.sh|*jvm*.sh|*optimization*.sh)
            echo "scripts/performance/"
            ;;
        *automation*.sh|*organize*.sh|*check*.sh)
            echo "scripts/automation/"
            ;;
        *)
            echo "scripts/development/"
            ;;
    esac
}

# 建议文档位置的函数
suggest_doc_location() {
    local file="$1"
    case "$file" in
        *report*.md|*fix*.md|*analysis*.md|*summary*.md|*完成报告*.md)
            echo "docs/reports/"
            ;;
        *guide*.md|*manual*.md|*training*.md|*指南*.md)
            echo "docs/guides/"
            ;;
        *architecture*.md|*design*.md|system-*.md)
            echo "docs/architecture/"
            ;;
        *deployment*.md|*deploy*.md)
            echo "docs/deployment/"
            ;;
        *development*.md|*implementation*.md|*refactor*.md)
            echo "docs/development/"
            ;;
        git-*.md|*git*.md)
            echo "docs/git/"
            ;;
        *security*.md|*auth*.md)
            echo "docs/security/"
            ;;
        *performance*.md|*optimization*.md)
            echo "docs/performance/"
            ;;
        *)
            echo "docs/guides/ 或其他适当分类"
            ;;
    esac
}

# 检查脚本命名规范
check_script_naming_convention() {
    local dir="$1"
    local invalid_names=0

    find "$dir" -name "*.sh" | while read script; do
        basename_script=$(basename "$script")
        # 检查是否包含空格或特殊字符
        if [[ "$basename_script" =~ [[:space:]] ]]; then
            log_warning "脚本名包含空格: $script"
            invalid_names=$((invalid_names + 1))
        fi

        # 检查是否使用了中文字符
        if [[ "$basename_script" =~ [一-龟] ]]; then
            log_warning "脚本名包含中文字符: $script"
            invalid_names=$((invalid_names + 1))
        fi
    done
}

# 检查是否有临时目录和文件
log_info "检查临时文件和目录..."
TEMP_DIRS=$(find . -maxdepth 1 -type d -name "temp*" -o -name "tmp*" -o -name "*backup*" -o -name ".*" | grep -v "^\.$" | grep -v "^\.git$" | wc -l)
if [ $TEMP_DIRS -gt 0 ]; then
    log_error "根目录发现临时/隐藏目录:"
    find . -maxdepth 1 -type d -name "temp*" -o -name "tmp*" -o -name "*backup*" -o -name ".*" | grep -v "^\.$" | grep -v "^\.git$"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    log_success "根目录无临时目录"
fi

if [ $VIOLATIONS -eq 0 ]; then
    log_success "根目录文件管控符合规范"
fi

echo ""
echo "📁 检查Scripts目录结构..."

# 检查scripts根目录是否有不应该存在的文件
log_info "检查scripts根目录清洁度..."
if [ -d "scripts" ]; then
    ROOT_FILES=$(find scripts -maxdepth 1 -type f | wc -l)
    if [ $ROOT_FILES -gt 0 ]; then
        log_error "scripts根目录发现 $ROOT_FILES 个文件，应该分类到子目录"
        find scripts -maxdepth 1 -type f | while read file; do
            echo "  违规文件: $file"
            echo "  建议位置: $(suggest_script_location "$file")"
        done
        VIOLATIONS=$((VIOLATIONS + 1))
    else
        log_success "scripts根目录清洁"
    fi
else
    log_error "缺失scripts目录"
    VIOLATIONS=$((VIOLATIONS + 1))
fi



# 检查必需的scripts子目录
REQUIRED_SCRIPT_DIRS=("automation" "database" "deployment" "development" "performance" "testing" "security")
log_info "检查必需的scripts子目录..."
for dir in "${REQUIRED_SCRIPT_DIRS[@]}"; do
    if [ ! -d "scripts/$dir" ]; then
        log_error "缺失必需目录: scripts/$dir"
        VIOLATIONS=$((VIOLATIONS + 1))
    else
        file_count=$(find "scripts/$dir" -name "*.sh" -o -name "*.sql" -o -name "*.conf" -o -name "*.bat" -o -name "*.html" -o -name "*.java" | wc -l)
        log_success "scripts/$dir 存在 ($file_count 个文件)"
        
        # 检查每个目录的文件是否符合命名规范
        check_script_naming_convention "scripts/$dir"
    fi
done



echo ""
echo "📁 检查Docs目录结构..."

# 检查docs根目录的文件
log_info "检查docs根目录结构..."
if [ -d "docs" ]; then
    # 允许在docs根目录的文件
    ALLOWED_DOCS_ROOT=("README.md" "REORGANIZED_STRUCTURE.md")
    
    find docs -maxdepth 1 -type f | while read file; do
        basename_file=$(basename "$file")
        allowed=false
        for allowed_file in "${ALLOWED_DOCS_ROOT[@]}"; do
            if [ "$basename_file" = "$allowed_file" ]; then
                allowed=true
                break
            fi
        done
        
        if [ "$allowed" = false ]; then
            log_warning "docs根目录发现非标准文件: $file"
            echo "  建议移动到: $(suggest_doc_location "$file")"
            WARNINGS=$((WARNINGS + 1))
        fi
    done
else
    log_error "缺失docs目录"
    VIOLATIONS=$((VIOLATIONS + 1))
fi



# 检查必需的docs子目录（本地办公模式更完整）
REQUIRED_DOC_DIRS=("architecture" "deployment" "development" "git" "guides" "reports" "security" "performance" "training" "fixes")
log_info "检查必需的docs子目录..."
for dir in "${REQUIRED_DOC_DIRS[@]}"; do
    if [ ! -d "docs/$dir" ]; then
        log_warning "建议创建目录: docs/$dir"
        SUGGESTIONS=$((SUGGESTIONS + 1))
    else
        file_count=$(find "docs/$dir" -name "*.md" | wc -l)
        log_success "docs/$dir 存在 ($file_count 个文档)"
    fi
done

echo ""
echo "🔍 检查文件命名规范..."

# 检查是否有中文目录名
log_info "检查目录命名规范..."
CHINESE_DIRS=$(find scripts docs -type d -name "*[一-龟]*" 2>/dev/null | wc -l)
if [ $CHINESE_DIRS -gt 0 ]; then
    log_error "发现中文目录名:"
    find scripts docs -type d -name "*[一-龟]*" 2>/dev/null
    VIOLATIONS=$((VIOLATIONS + 1))
else
    log_success "目录命名规范"
fi

# 检查是否有临时文件
log_info "检查临时文件..."
TEMP_FILES=$(find scripts docs -name "*.tmp" -o -name "*.temp" -o -name "*~" -o -name "*.bak" 2>/dev/null | wc -l)
if [ $TEMP_FILES -gt 0 ]; then
    log_warning "发现临时文件:"
    find scripts docs -name "*.tmp" -o -name "*.temp" -o -name "*~" -o -name "*.bak" 2>/dev/null
    WARNINGS=$((WARNINGS + 1))
else
    log_success "无临时文件"
fi

# 检查文件权限
log_info "检查脚本文件权限..."
EXECUTABLE_SCRIPTS=$(find scripts -name "*.sh" -executable 2>/dev/null | wc -l)
TOTAL_SCRIPTS=$(find scripts -name "*.sh" 2>/dev/null | wc -l)
if [ $TOTAL_SCRIPTS -gt 0 ]; then
    if [ $EXECUTABLE_SCRIPTS -eq $TOTAL_SCRIPTS ]; then
        log_success "所有脚本文件都有执行权限"
    else
        NON_EXECUTABLE=$((TOTAL_SCRIPTS - EXECUTABLE_SCRIPTS))
        log_warning "发现 $NON_EXECUTABLE 个脚本文件缺少执行权限"
        find scripts -name "*.sh" ! -executable 2>/dev/null | while read script; do
            echo "  缺少执行权限: $script"
        done
        WARNINGS=$((WARNINGS + 1))
    fi
fi

echo ""
echo "📊 检查结果统计..."

# 统计各目录文件数量
echo "Scripts目录统计:"
for dir in automation database deployment development performance testing security; do
    if [ -d "scripts/$dir" ]; then
        count=$(find "scripts/$dir" -type f 2>/dev/null | wc -l)
        echo "  - $dir: $count 个文件"
    else
        echo "  - $dir: 目录不存在"
    fi
done

echo ""
echo "Docs目录统计:"
for dir in architecture deployment development git guides reports security performance training fixes; do
    if [ -d "docs/$dir" ]; then
        count=$(find "docs/$dir" -name "*.md" 2>/dev/null | wc -l)
        echo "  - $dir: $count 个文档"
    else
        echo "  - $dir: 目录不存在"
    fi
done

echo ""
echo "🎯 [LocalOfficeModel] 检查总结:"
echo "  违规项: $VIOLATIONS"
echo "  警告项: $WARNINGS" 
echo "  建议项: $SUGGESTIONS"

if [ $VIOLATIONS -eq 0 ] && [ $WARNINGS -eq 0 ] && [ $SUGGESTIONS -eq 0 ]; then
    log_success "🎉 恭喜！文件组织完全符合LocalOfficeModel规范！"
    exit 0
elif [ $VIOLATIONS -eq 0 ] && [ $SUGGESTIONS -gt 0 ]; then
    log_warning "⚠️  发现 $WARNINGS 个警告和 $SUGGESTIONS 个改进建议"
    echo ""
    echo "建议运行整理脚本："
    echo "  ./scripts/automation/LocalOfficeModel-organize-task-files.sh"
    exit 1
else
    log_error "❌ 发现 $VIOLATIONS 个违规、$WARNINGS 个警告和 $SUGGESTIONS 个建议，必须立即修复！"
    echo ""
    echo "建议运行整理脚本："
    echo "  ./scripts/automation/LocalOfficeModel-organize-task-files.sh"
    exit 2
fi
