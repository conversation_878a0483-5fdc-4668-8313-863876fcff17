#!/bin/bash

# 任务完成后的文件整理脚本
# 用于自动将任务生成的文件分类存放到正确目录

echo "📁 开始整理任务生成的文件..."

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 计数器
MOVED_FILES=0

# 检查当前目录是否有需要整理的文件
echo "🔍 扫描当前目录中的文件..."

# 整理脚本文件
echo ""
echo -e "${BLUE}📝 整理脚本文件...${NC}"

# 自动化脚本
for file in update-*.sh install-*.sh start-*.sh stop-*.sh *-packages.sh; do
    if [ -f "$file" ]; then
        mv "$file" scripts/automation/
        echo -e "${GREEN}✅ 移动自动化脚本: $file → scripts/automation/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 数据库脚本
for file in *.sql create_*.sql alter_*.sql check_*.sql test_*.sql; do
    if [ -f "$file" ]; then
        mv "$file" scripts/database/
        echo -e "${GREEN}✅ 移动数据库脚本: $file → scripts/database/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 部署脚本
for file in deploy*.sh deploy*.bat *.conf nginx*.conf; do
    if [ -f "$file" ]; then
        mv "$file" scripts/deployment/
        echo -e "${GREEN}✅ 移动部署脚本: $file → scripts/deployment/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 测试脚本
for file in test-*.sh test_*.sh verify-*.sh *-test.sh *.html; do
    if [ -f "$file" ]; then
        mv "$file" scripts/testing/
        echo -e "${GREEN}✅ 移动测试脚本: $file → scripts/testing/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 性能脚本
for file in *performance*.sh *jvm*.sh *optimization*.sh; do
    if [ -f "$file" ]; then
        mv "$file" scripts/performance/
        echo -e "${GREEN}✅ 移动性能脚本: $file → scripts/performance/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 其他开发脚本
for file in quick-*.sh generate-*.sh build-*.sh; do
    if [ -f "$file" ]; then
        mv "$file" scripts/development/
        echo -e "${GREEN}✅ 移动开发脚本: $file → scripts/development/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 整理文档文件
echo ""
echo -e "${BLUE}📚 整理文档文件...${NC}"

# 架构文档
for file in *architecture*.md *design*.md system-*.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/architecture/
        echo -e "${GREEN}✅ 移动架构文档: $file → docs/architecture/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 开发文档
for file in *refactor*.md *development*.md *implementation*.md distributed-*.md controller-*.md module-*.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/development/
        echo -e "${GREEN}✅ 移动开发文档: $file → docs/development/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 部署文档
for file in *deployment*.md *deploy*.md docker-*.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/deployment/
        echo -e "${GREEN}✅ 移动部署文档: $file → docs/deployment/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# Git文档
for file in git-*.md *git*.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/git/
        echo -e "${GREEN}✅ 移动Git文档: $file → docs/git/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 报告文档
for file in *report*.md *fix*.md *analysis*.md *summary*.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/reports/
        echo -e "${GREEN}✅ 移动报告文档: $file → docs/reports/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

# 指南文档
for file in *guide*.md *manual*.md *training*.md *standards*.md; do
    if [ -f "$file" ]; then
        mv "$file" docs/guides/
        echo -e "${GREEN}✅ 移动指南文档: $file → docs/guides/${NC}"
        MOVED_FILES=$((MOVED_FILES + 1))
    fi
done

echo ""
echo "📊 整理结果统计:"
echo -e "${GREEN}✅ 共移动了 $MOVED_FILES 个文件到正确位置${NC}"

if [ $MOVED_FILES -eq 0 ]; then
    echo -e "${GREEN}🎉 所有文件已正确分类，无需整理！${NC}"
else
    echo -e "${YELLOW}📋 建议运行检查脚本验证整理结果:${NC}"
    echo "   ./scripts/automation/check-file-organization.sh"
fi

echo ""
echo "✅ 文件整理完成！"
