#!/bin/bash

# =============================================================================
# LocalOfficeModel - 任务完成后的文件整理脚本
# 功能：自动将任务生成的文件分类存放到正确目录（本地办公模式）
# 作者：EDC Research Team
# 创建时间：$(date +%Y-%m-%d)
# 模式：本地办公模式 (LocalOfficeModel)
# =============================================================================

echo "📁 [LocalOfficeModel] 开始整理任务生成的文件..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 计数器
MOVED_FILES=0
CREATED_DIRS=0
FIXED_PERMISSIONS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[LocalOfficeModel-INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[LocalOfficeModel-SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[LocalOfficeModel-WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[LocalOfficeModel-ERROR]${NC} $1"
}

# 创建必需目录的函数
create_required_directories() {
    log_info "检查并创建必需的目录结构..."
    
    # Scripts目录结构
    SCRIPT_DIRS=("scripts/automation" "scripts/database" "scripts/deployment" "scripts/development" "scripts/performance" "scripts/testing" "scripts/security")
    
    # Docs目录结构
    DOC_DIRS=("docs/architecture" "docs/deployment" "docs/development" "docs/git" "docs/guides" "docs/reports" "docs/security" "docs/performance" "docs/training" "docs/fixes")
    
    for dir in "${SCRIPT_DIRS[@]}" "${DOC_DIRS[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
            CREATED_DIRS=$((CREATED_DIRS + 1))
        fi
    done
}

# 移动文件的安全函数
safe_move_file() {
    local source="$1"
    local target_dir="$2"
    local description="$3"
    
    if [ -f "$source" ]; then
        # 确保目标目录存在
        mkdir -p "$target_dir"
        
        # 检查目标文件是否已存在
        local basename_file=$(basename "$source")
        local target_file="$target_dir/$basename_file"
        
        if [ -f "$target_file" ]; then
            log_warning "目标文件已存在: $target_file"
            # 创建备份
            local backup_file="$target_file.backup.$(date +%Y%m%d_%H%M%S)"
            mv "$target_file" "$backup_file"
            log_info "原文件备份为: $backup_file"
        fi
        
        mv "$source" "$target_dir/"
        log_success "移动$description: $source → $target_dir/"
        MOVED_FILES=$((MOVED_FILES + 1))
        
        # 如果是脚本文件，设置执行权限
        if [[ "$source" == *.sh ]]; then
            chmod +x "$target_dir/$basename_file"
            FIXED_PERMISSIONS=$((FIXED_PERMISSIONS + 1))
        fi
    fi
}

# 检查当前目录是否有需要整理的文件
log_info "扫描当前目录中的文件..."

# 创建必需目录
create_required_directories

# 整理脚本文件
echo ""
log_info "整理脚本文件..."

# 自动化脚本（更精确的匹配）
for file in update-*.sh install-*.sh start-*.sh stop-*.sh *-packages.sh setup-*.sh configure-*.sh *automation*.sh *organize*.sh *check*.sh; do
    safe_move_file "$file" "scripts/automation" "自动化脚本"
done

# 数据库脚本
for file in *.sql create_*.sql alter_*.sql check_*.sql test_*.sql insert_*.sql update_*.sql delete_*.sql *database*.sql *db*.sql; do
    safe_move_file "$file" "scripts/database" "数据库脚本"
done

# 部署脚本
for file in deploy*.sh deploy*.bat *.conf nginx*.conf apache*.conf docker*.sh k8s*.sh kubernetes*.sh *deployment*.sh; do
    safe_move_file "$file" "scripts/deployment" "部署脚本"
done

# 测试脚本
for file in test-*.sh test_*.sh verify-*.sh *-test.sh *test*.html junit*.sh selenium*.sh; do
    safe_move_file "$file" "scripts/testing" "测试脚本"
done

# 性能脚本
for file in *performance*.sh *jvm*.sh *optimization*.sh *benchmark*.sh *profiling*.sh *monitoring*.sh; do
    safe_move_file "$file" "scripts/performance" "性能脚本"
done

# 安全脚本
for file in *security*.sh *auth*.sh *ssl*.sh *cert*.sh *firewall*.sh *audit*.sh; do
    safe_move_file "$file" "scripts/security" "安全脚本"
done

# 其他开发脚本
for file in quick-*.sh generate-*.sh build-*.sh compile-*.sh package-*.sh clean-*.sh; do
    safe_move_file "$file" "scripts/development" "开发脚本"
done

# 整理文档文件
echo ""
log_info "整理文档文件..."

# 架构文档
for file in *architecture*.md *design*.md system-*.md *架构*.md *设计*.md; do
    safe_move_file "$file" "docs/architecture" "架构文档"
done

# 开发文档
for file in *refactor*.md *development*.md *implementation*.md distributed-*.md controller-*.md module-*.md *开发*.md *实现*.md *重构*.md; do
    safe_move_file "$file" "docs/development" "开发文档"
done

# 部署文档
for file in *deployment*.md *deploy*.md docker-*.md k8s*.md kubernetes*.md *部署*.md; do
    safe_move_file "$file" "docs/deployment" "部署文档"
done

# Git文档
for file in git-*.md *git*.md; do
    safe_move_file "$file" "docs/git" "Git文档"
done

# 报告文档（包含中文文件名）
for file in *report*.md *fix*.md *analysis*.md *summary*.md *报告*.md *修复*.md *分析*.md *完成报告*.md; do
    safe_move_file "$file" "docs/reports" "报告文档"
done

# 指南文档
for file in *guide*.md *manual*.md *training*.md *standards*.md *指南*.md *手册*.md *培训*.md *标准*.md; do
    safe_move_file "$file" "docs/guides" "指南文档"
done

# 安全文档
for file in *security*.md *auth*.md *permission*.md *安全*.md *权限*.md *认证*.md; do
    safe_move_file "$file" "docs/security" "安全文档"
done

# 性能文档
for file in *performance*.md *optimization*.md *benchmark*.md *性能*.md *优化*.md; do
    safe_move_file "$file" "docs/performance" "性能文档"
done

# 修复文档
for file in *fix-report*.md *bug-fix*.md *issue-*.md *problem-*.md *solution-*.md; do
    safe_move_file "$file" "docs/fixes" "修复文档"
done

# 培训文档
for file in *training*.md *tutorial*.md *workshop*.md *course*.md *lesson*.md; do
    safe_move_file "$file" "docs/training" "培训文档"
done

# 整理配置文件
echo ""
log_info "整理配置文件..."

# 应用配置文件
for file in application*.yml application*.yaml application*.properties config*.yml config*.yaml; do
    if [ -f "$file" ]; then
        # 配置文件通常应该在相应的模块中，这里只是提示
        log_warning "发现配置文件: $file - 请手动移动到适当的模块配置目录"
    fi
done

# 整理临时文件和备份文件
echo ""
log_info "清理临时文件..."

# 删除临时文件
for file in *.tmp *.temp *~ *.bak; do
    if [ -f "$file" ]; then
        rm -f "$file"
        log_success "删除临时文件: $file"
    fi
done

# 修复脚本权限
echo ""
log_info "修复脚本文件权限..."

find scripts -name "*.sh" ! -executable -exec chmod +x {} \; 2>/dev/null
SCRIPT_PERMISSION_FIXES=$(find scripts -name "*.sh" -executable 2>/dev/null | wc -l)
if [ $SCRIPT_PERMISSION_FIXES -gt 0 ]; then
    log_success "修复了脚本文件执行权限"
    FIXED_PERMISSIONS=$((FIXED_PERMISSIONS + SCRIPT_PERMISSION_FIXES))
fi

# 检查是否有遗漏的文件
echo ""
log_info "检查是否有遗漏的文件..."

REMAINING_FILES=0
for file in *.md *.sh *.sql *.yml *.yaml *.conf *.html *.txt; do
    if [ -f "$file" ]; then
        # 排除允许在根目录的文件
        if [[ "$file" != "README.md" && "$file" != "pom.xml" && "$file" != "research-master.iml" && "$file" != ".gitignore" ]]; then
            log_warning "根目录仍有文件需要手动处理: $file"
            echo "  建议位置: $(suggest_location "$file")"
            REMAINING_FILES=$((REMAINING_FILES + 1))
        fi
    fi
done

# 建议文件位置
suggest_location() {
    local file="$1"
    case "$file" in
        *.sql) echo "scripts/database/" ;;
        *test*.sh|*verify*.sh) echo "scripts/testing/" ;;
        *deploy*.sh) echo "scripts/deployment/" ;;
        *performance*.sh) echo "scripts/performance/" ;;
        *.sh) echo "scripts/automation/ 或其他适当的scripts子目录" ;;
        *report*.md|*fix*.md) echo "docs/reports/" ;;
        *guide*.md|*manual*.md) echo "docs/guides/" ;;
        *architecture*.md) echo "docs/architecture/" ;;
        *.md) echo "docs/ 的适当子目录" ;;
        *.yml|*.yaml|*.conf) echo "相应模块的配置目录" ;;
        *) echo "适当的分类目录" ;;
    esac
}

echo ""
echo "📊 [LocalOfficeModel] 整理结果统计:"
log_success "创建目录: $CREATED_DIRS 个"
log_success "移动文件: $MOVED_FILES 个"
log_success "修复权限: $FIXED_PERMISSIONS 个"

if [ $REMAINING_FILES -gt 0 ]; then
    log_warning "仍有 $REMAINING_FILES 个文件需要手动处理"
fi

if [ $MOVED_FILES -eq 0 ] && [ $CREATED_DIRS -eq 0 ]; then
    log_success "🎉 所有文件已正确分类，无需整理！"
else
    log_info "📋 建议运行检查脚本验证整理结果:"
    echo "   ./scripts/automation/LocalOfficeModel-check-file-organization.sh"
fi

# 生成整理报告
echo ""
log_info "生成整理报告..."
REPORT_FILE="docs/reports/file-organization-report-$(date +%Y%m%d_%H%M%S).md"
mkdir -p "docs/reports"

cat > "$REPORT_FILE" << EOF
# LocalOfficeModel 文件整理报告

**整理时间**: $(date '+%Y-%m-%d %H:%M:%S')
**整理模式**: LocalOfficeModel (本地办公模式)

## 整理统计

- 创建目录: $CREATED_DIRS 个
- 移动文件: $MOVED_FILES 个  
- 修复权限: $FIXED_PERMISSIONS 个
- 遗留文件: $REMAINING_FILES 个

## 目录结构验证

建议运行以下命令验证整理结果：
\`\`\`bash
./scripts/automation/LocalOfficeModel-check-file-organization.sh
\`\`\`

## 后续建议

1. 定期运行文件整理脚本
2. 遵循项目文件组织规范
3. 及时清理临时文件
4. 保持目录结构清洁

---
*报告由 LocalOfficeModel-organize-task-files.sh 自动生成*
EOF

log_success "整理报告已生成: $REPORT_FILE"

echo ""
log_success "✅ [LocalOfficeModel] 文件整理完成！"
