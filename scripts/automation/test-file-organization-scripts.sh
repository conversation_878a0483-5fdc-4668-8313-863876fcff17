#!/bin/bash

# =============================================================================
# 文件组织脚本测试工具
# 功能：测试所有文件组织自动化脚本的功能
# 作者：EDC Research Team
# 创建时间：$(date +%Y-%m-%d)
# =============================================================================

echo "🧪 开始测试文件组织自动化脚本..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[TEST-INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[TEST-SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[TEST-WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[TEST-ERROR]${NC} $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "测试 $TOTAL_TESTS: $test_name"
    
    if eval "$test_command"; then
        if [ "$expected_result" = "success" ]; then
            log_success "✅ $test_name - 通过"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            log_error "❌ $test_name - 预期失败但实际成功"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            log_success "✅ $test_name - 通过（预期失败）"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            log_error "❌ $test_name - 失败"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi
    echo
}

echo "========================================"
echo "文件组织脚本测试套件"
echo "========================================"
echo

# 测试1: 检查脚本文件是否存在
log_info "阶段1: 检查脚本文件存在性"
echo

run_test "TelecommutingMode检查脚本存在" \
    "[ -f 'scripts/automation/TelecommutingMode-check-file-organization.sh' ]" \
    "success"

run_test "TelecommutingMode整理脚本存在" \
    "[ -f 'scripts/automation/TelecommutingMode-organize-task-files.sh' ]" \
    "success"

run_test "LocalOfficeModel检查脚本存在" \
    "[ -f 'scripts/automation/LocalOfficeModel-check-file-organization.sh' ]" \
    "success"

run_test "LocalOfficeModel整理脚本存在" \
    "[ -f 'scripts/automation/LocalOfficeModel-organize-task-files.sh' ]" \
    "success"

# 测试2: 检查脚本权限
log_info "阶段2: 检查脚本执行权限"
echo

run_test "TelecommutingMode检查脚本可执行" \
    "[ -x 'scripts/automation/TelecommutingMode-check-file-organization.sh' ]" \
    "success"

run_test "TelecommutingMode整理脚本可执行" \
    "[ -x 'scripts/automation/TelecommutingMode-organize-task-files.sh' ]" \
    "success"

run_test "LocalOfficeModel检查脚本可执行" \
    "[ -x 'scripts/automation/LocalOfficeModel-check-file-organization.sh' ]" \
    "success"

run_test "LocalOfficeModel整理脚本可执行" \
    "[ -x 'scripts/automation/LocalOfficeModel-organize-task-files.sh' ]" \
    "success"

# 测试3: 检查脚本语法
log_info "阶段3: 检查脚本语法"
echo

run_test "TelecommutingMode检查脚本语法" \
    "bash -n scripts/automation/TelecommutingMode-check-file-organization.sh" \
    "success"

run_test "TelecommutingMode整理脚本语法" \
    "bash -n scripts/automation/TelecommutingMode-organize-task-files.sh" \
    "success"

run_test "LocalOfficeModel检查脚本语法" \
    "bash -n scripts/automation/LocalOfficeModel-check-file-organization.sh" \
    "success"

run_test "LocalOfficeModel整理脚本语法" \
    "bash -n scripts/automation/LocalOfficeModel-organize-task-files.sh" \
    "success"

# 测试4: 检查脚本功能（干运行）
log_info "阶段4: 检查脚本基本功能"
echo

# 创建测试环境
TEST_DIR="test_file_organization_$$"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

# 创建测试文件结构
mkdir -p scripts/automation docs
touch test-file.md
touch deploy-script.sh
touch database-query.sql
touch README.md

log_info "创建测试环境: $TEST_DIR"

# 测试LocalOfficeModel检查脚本
run_test "LocalOfficeModel检查脚本功能测试" \
    "../scripts/automation/LocalOfficeModel-check-file-organization.sh > /dev/null 2>&1; [ \$? -eq 2 ]" \
    "success"

# 测试LocalOfficeModel整理脚本
run_test "LocalOfficeModel整理脚本功能测试" \
    "../scripts/automation/LocalOfficeModel-organize-task-files.sh > /dev/null 2>&1; [ \$? -eq 0 ]" \
    "success"

# 清理测试环境
cd ..
rm -rf "$TEST_DIR"
log_info "清理测试环境: $TEST_DIR"

# 测试5: 检查文档是否存在
log_info "阶段5: 检查相关文档"
echo

run_test "使用指南文档存在" \
    "[ -f 'docs/guides/file-organization-automation-guide.md' ]" \
    "success"

# 测试6: 检查脚本前缀命名
log_info "阶段6: 检查脚本命名规范"
echo

run_test "TelecommutingMode脚本命名规范" \
    "[ \$(ls scripts/automation/TelecommutingMode-*.sh | wc -l) -eq 2 ]" \
    "success"

run_test "LocalOfficeModel脚本命名规范" \
    "[ \$(ls scripts/automation/LocalOfficeModel-*.sh | wc -l) -eq 2 ]" \
    "success"

# 测试7: 检查脚本内容特征
log_info "阶段7: 检查脚本内容特征"
echo

run_test "LocalOfficeModel脚本包含增强功能" \
    "grep -q 'LocalOfficeModel' scripts/automation/LocalOfficeModel-*.sh" \
    "success"

run_test "TelecommutingMode脚本保持原有功能" \
    "grep -q 'TelecommutingMode' scripts/automation/TelecommutingMode-*.sh || true" \
    "success"

# 生成测试报告
echo "========================================"
echo "测试结果统计"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo "通过测试: $PASSED_TESTS"
echo "失败测试: $FAILED_TESTS"
echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
echo

if [ $FAILED_TESTS -eq 0 ]; then
    log_success "🎉 所有测试通过！文件组织脚本功能正常"
    echo
    echo "可用的脚本："
    echo "1. 远程办公模式："
    echo "   ./scripts/automation/TelecommutingMode-check-file-organization.sh"
    echo "   ./scripts/automation/TelecommutingMode-organize-task-files.sh"
    echo
    echo "2. 本地办公模式："
    echo "   ./scripts/automation/LocalOfficeModel-check-file-organization.sh"
    echo "   ./scripts/automation/LocalOfficeModel-organize-task-files.sh"
    echo
    echo "3. 使用指南："
    echo "   cat docs/guides/file-organization-automation-guide.md"
    
    exit 0
else
    log_error "❌ 发现 $FAILED_TESTS 个测试失败，请检查并修复"
    exit 1
fi
