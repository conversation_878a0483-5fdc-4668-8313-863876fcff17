#!/bin/bash

# 文件组织规范检查脚本
# 用于检查项目文件是否按照规范正确分类存放

echo "🔍 开始检查文件组织规范..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果统计
VIOLATIONS=0
WARNINGS=0

echo ""
echo "🚨 检查根目录文件管控..."

# 定义允许在根目录存在的文件
ALLOWED_ROOT_FILES=("README.md" "pom.xml")

# 检查根目录是否有不应该存在的文件
echo "检查项目根目录..."
for file in *.md *.yml *.yaml *.conf *.cnf *.html *.sh *.sql *.java *.xml; do
    if [ -f "$file" ]; then
        # 检查是否在允许列表中
        allowed=false
        for allowed_file in "${ALLOWED_ROOT_FILES[@]}"; do
            if [ "$file" = "$allowed_file" ]; then
                allowed=true
                break
            fi
        done

        if [ "$allowed" = false ]; then
            echo -e "${RED}❌ 违规: 根目录发现不允许的文件: $file${NC}"
            VIOLATIONS=$((VIOLATIONS + 1))
        fi
    fi
done

# 检查是否有临时目录
TEMP_DIRS=$(find . -maxdepth 1 -type d -name "temp*" -o -name "tmp*" -o -name "*backup*" | wc -l)
if [ $TEMP_DIRS -gt 0 ]; then
    echo -e "${RED}❌ 违规: 根目录发现临时目录:${NC}"
    find . -maxdepth 1 -type d -name "temp*" -o -name "tmp*" -o -name "*backup*"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ 根目录无临时目录${NC}"
fi

if [ $VIOLATIONS -eq 0 ]; then
    echo -e "${GREEN}✅ 根目录文件管控符合规范${NC}"
fi

echo ""
echo "📁 检查Scripts目录结构..."

# 检查scripts根目录是否有不应该存在的文件
echo "检查scripts根目录..."
ROOT_FILES=$(find scripts -maxdepth 1 -type f | wc -l)
if [ $ROOT_FILES -gt 0 ]; then
    echo -e "${RED}❌ 违规: scripts根目录发现 $ROOT_FILES 个文件${NC}"
    find scripts -maxdepth 1 -type f
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ scripts根目录清洁${NC}"
fi

# 检查必需的scripts子目录
REQUIRED_SCRIPT_DIRS=("automation" "database" "deployment" "development" "performance" "testing")
for dir in "${REQUIRED_SCRIPT_DIRS[@]}"; do
    if [ ! -d "scripts/$dir" ]; then
        echo -e "${RED}❌ 缺失必需目录: scripts/$dir${NC}"
        VIOLATIONS=$((VIOLATIONS + 1))
    else
        file_count=$(find "scripts/$dir" -name "*.sh" -o -name "*.sql" -o -name "*.conf" -o -name "*.bat" -o -name "*.html" -o -name "*.java" | wc -l)
        echo -e "${GREEN}✅ scripts/$dir 存在 ($file_count 个文件)${NC}"
    fi
done

echo ""
echo "📁 检查Docs目录结构..."

# 检查docs根目录的文件
echo "检查docs根目录..."
DOCS_ROOT_FILES=$(find docs -maxdepth 1 -type f | grep -v "README.md" | grep -v "REORGANIZED_STRUCTURE.md" | wc -l)
if [ $DOCS_ROOT_FILES -gt 0 ]; then
    echo -e "${YELLOW}⚠️  警告: docs根目录发现 $DOCS_ROOT_FILES 个非标准文件${NC}"
    find docs -maxdepth 1 -type f | grep -v "README.md" | grep -v "REORGANIZED_STRUCTURE.md"
    WARNINGS=$((WARNINGS + 1))
else
    echo -e "${GREEN}✅ docs根目录结构规范${NC}"
fi

# 检查必需的docs子目录
REQUIRED_DOC_DIRS=("architecture" "deployment" "development" "git" "guides" "reports")
for dir in "${REQUIRED_DOC_DIRS[@]}"; do
    if [ ! -d "docs/$dir" ]; then
        echo -e "${RED}❌ 缺失必需目录: docs/$dir${NC}"
        VIOLATIONS=$((VIOLATIONS + 1))
    else
        file_count=$(find "docs/$dir" -name "*.md" | wc -l)
        echo -e "${GREEN}✅ docs/$dir 存在 ($file_count 个文档)${NC}"
    fi
done

echo ""
echo "🔍 检查文件命名规范..."

# 检查是否有中文目录名
CHINESE_DIRS=$(find scripts docs -type d -name "*[一-龟]*" | wc -l)
if [ $CHINESE_DIRS -gt 0 ]; then
    echo -e "${RED}❌ 发现中文目录名:${NC}"
    find scripts docs -type d -name "*[一-龟]*"
    VIOLATIONS=$((VIOLATIONS + 1))
else
    echo -e "${GREEN}✅ 无中文目录名${NC}"
fi

# 检查是否有临时文件
TEMP_FILES=$(find scripts docs -name "*.tmp" -o -name "*.temp" -o -name "*~" | wc -l)
if [ $TEMP_FILES -gt 0 ]; then
    echo -e "${YELLOW}⚠️  发现临时文件:${NC}"
    find scripts docs -name "*.tmp" -o -name "*.temp" -o -name "*~"
    WARNINGS=$((WARNINGS + 1))
else
    echo -e "${GREEN}✅ 无临时文件${NC}"
fi

echo ""
echo "📊 检查结果统计..."

# 统计各目录文件数量
echo "Scripts目录统计:"
for dir in automation database deployment development performance testing; do
    if [ -d "scripts/$dir" ]; then
        count=$(find "scripts/$dir" -type f | wc -l)
        echo "  - $dir: $count 个文件"
    fi
done

echo ""
echo "Docs目录统计:"
for dir in architecture deployment development git guides reports; do
    if [ -d "docs/$dir" ]; then
        count=$(find "docs/$dir" -name "*.md" | wc -l)
        echo "  - $dir: $count 个文档"
    fi
done

echo ""
echo "🎯 检查总结:"
if [ $VIOLATIONS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    echo -e "${GREEN}🎉 恭喜！文件组织完全符合规范！${NC}"
    exit 0
elif [ $VIOLATIONS -eq 0 ]; then
    echo -e "${YELLOW}⚠️  发现 $WARNINGS 个警告，建议处理${NC}"
    exit 1
else
    echo -e "${RED}❌ 发现 $VIOLATIONS 个违规和 $WARNINGS 个警告，必须立即修复！${NC}"
    exit 2
fi
