package com.haoys.user.domain.vo.participant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.ecrf.TemplateFormDictionaryVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectParticipantViewConfigVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;
    
    @ApiModelProperty(value = "参与者id")
    private String testeeId;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "系统用户id")
    private Long userId;

    @ApiModelProperty(value = "bdp用户id")
    private String bdpUserId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "姓名缩写")
    private String acronym;

    @ApiModelProperty(value = "参与者code")
    private String code;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "参与者绑定配置")
    private String testeeConfig;

    @ApiModelProperty(value = "性别 男、女、未知")
    private String gender;

    @ApiModelProperty(value = "身份证号码")
    private String idcard;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "联系方式")
    private String contant;

    @ApiModelProperty(value = "实际年龄")
    private Integer age;

    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;

    @ApiModelProperty(value = "知情日期")
    private Date informedDate;

    @ApiModelProperty(value = "参与者研究状态 参照字典说明")
    private String researchStatus;

    @ApiModelProperty(value = "数据状态0/1")
    private String status;

    @ApiModelProperty(value = "扩展信息")
    private String expand;

    @ApiModelProperty(value = "所属表单")
    private Long resourceFormId;

    @ApiModelProperty(value = "审核状态")
    private String reviewStatus;

    @ApiModelProperty(value = "创建人")
    private String createUserId;

    @ApiModelProperty(value = "参与者基本信息动态字段集合")
    private List<ProjectParticipantViewConfigVo.FormVariableConfig> formVariableConfigArrayList;

    @Data
    public static class FormVariableConfig {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "字段名称")
        private Long id;
        
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        private Long testeeResultId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "表单项id")
        private Long formId;

        @ApiModelProperty(value = "表单变量类型")
        private String type;

        @ApiModelProperty(value = "字段名称")
        private String label;

        @ApiModelProperty(value = "数据库字段")
        private String fieldName;

        @ApiModelProperty(value = "字段英文名称")
        private String langValue;

        @ApiModelProperty(value = "字段标题")
        private String title;

        @ApiModelProperty(value = "字段内容")
        private String content;

        @ApiModelProperty(value = "是否隐藏0/1")
        private Boolean hidden;

        @ApiModelProperty(value = "绑定属性")
        private String model;

        @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
        private String requireType;

        @ApiModelProperty(value = "是否必填项0/1")
        private Boolean required;

        @ApiModelProperty(value = "是否显示字段名称0/1")
        private Boolean showTitle;

        @ApiModelProperty(value = "是否显示内容0/1")
        private Boolean showContent;

        @ApiModelProperty(value = "文字提示")
        private String placeholder;

        @ApiModelProperty(value = "变量图标")
        private String icon;

        @ApiModelProperty(value = "验证规则")
        private String rules;

        @ApiModelProperty(value = "字典来源(1-系统字典2-表单字典 3-数据单位)")
        private String dicResource;

        @ApiModelProperty(value = "引用字典id")
        private String refDicId;

        @ApiModelProperty(value = "字典默认值")
        private String defaultDicValue;

        @ApiModelProperty(value = "变量默认值")
        private String defaultValue;

        @ApiModelProperty(value = "量表打分设置")
        private BigDecimal scoreValue;

        @ApiModelProperty(value = "计量单位")
        private String unitValue;

        @ApiModelProperty(value = "字段样式")
        private String styleData;

        @ApiModelProperty(value = "控件尺寸")
        private String panelSize;

        @ApiModelProperty(value = "参与者自定义标题")
        private Boolean customTestee;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "自定义标题映射源id")
        private Long baseVariableValueId;

        @ApiModelProperty(value = "扩展字段")
        private String expand;

        @ApiModelProperty(value = "变量排序")
        private Integer sort;

        @ApiModelProperty(value = "数据状态0/1")
        private String status;

        @ApiModelProperty(value = "表单输入值")
        private String value = "";

        @ApiModelProperty(value = "字典选项内容")
        private List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();


    }
}
