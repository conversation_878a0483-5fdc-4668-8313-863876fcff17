package com.haoys.quartz.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.quartz.domain.SystemRequestRecordJobLog;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务执行日志服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface ISystemRequestRecordJobLogService {
    
    /**
     * 查询定时任务执行日志列表
     *
     * @param jobLog 定时任务执行日志信息
     * @return 定时任务执行日志集合
     */
    List<SystemRequestRecordJobLog> selectJobLogList(SystemRequestRecordJobLog jobLog);

    /**
     * 查询定时任务执行日志列表（分页）
     *
     * @param jobLog 定时任务执行日志信息
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    CommonPage<SystemRequestRecordJobLog> selectJobLogListWithPage(SystemRequestRecordJobLog jobLog, Integer pageNum, Integer pageSize);
    
    /**
     * 通过定时任务执行日志ID查询定时任务执行日志信息
     * 
     * @param logId 定时任务执行日志ID
     * @return 定时任务执行日志信息
     */
    SystemRequestRecordJobLog selectJobLogById(Long logId);
    
    /**
     * 新增定时任务执行日志信息
     * 
     * @param jobLog 定时任务执行日志信息
     * @return 结果
     */
    int insertJobLog(SystemRequestRecordJobLog jobLog);
    
    /**
     * 批量删除定时任务执行日志信息
     * 
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    int deleteJobLogByIds(Long[] logIds);
    
    /**
     * 删除定时任务执行日志信息
     * 
     * @param logId 定时任务执行日志ID
     * @return 结果
     */
    int deleteJobLogById(Long logId);
    
    /**
     * 根据任务ID删除执行日志
     * 
     * @param jobId 任务ID
     * @return 结果
     */
    int deleteJobLogByJobId(Long jobId);
    
    /**
     * 清空定时任务执行日志
     * 
     * @return 结果
     */
    int cleanJobLog();
    
    /**
     * 查询指定任务的执行日志列表
     * 
     * @param jobId 任务ID
     * @return 执行日志集合
     */
    List<SystemRequestRecordJobLog> selectJobLogListByJobId(Long jobId);
    
    /**
     * 查询指定任务的最新执行日志
     * 
     * @param jobId 任务ID
     * @return 执行日志
     */
    SystemRequestRecordJobLog selectLatestJobLogByJobId(Long jobId);
    
    /**
     * 查询指定任务的最新成功执行日志
     * 
     * @param jobId 任务ID
     * @return 执行日志
     */
    SystemRequestRecordJobLog selectLatestSuccessJobLogByJobId(Long jobId);
    
    /**
     * 查询指定任务的最新失败执行日志
     * 
     * @param jobId 任务ID
     * @return 执行日志
     */
    SystemRequestRecordJobLog selectLatestFailureJobLogByJobId(Long jobId);
    
    /**
     * 查询执行失败的日志列表
     * 
     * @return 执行日志集合
     */
    List<SystemRequestRecordJobLog> selectFailureJobLogList();
    
    /**
     * 查询执行时间超过阈值的日志列表
     * 
     * @param threshold 时间阈值（毫秒）
     * @return 执行日志集合
     */
    List<SystemRequestRecordJobLog> selectSlowJobLogList(long threshold);
    
    /**
     * 查询指定时间范围内的执行日志列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行日志集合
     */
    List<SystemRequestRecordJobLog> selectJobLogListByTimeRange(Date startTime, Date endTime);
    
    /**
     * 查询指定任务类型的执行日志列表
     * 
     * @param jobType 任务类型
     * @return 执行日志集合
     */
    List<SystemRequestRecordJobLog> selectJobLogListByType(Integer jobType);
    
    /**
     * 查询指定执行节点的日志列表
     * 
     * @param executeNode 执行节点
     * @return 执行日志集合
     */
    List<SystemRequestRecordJobLog> selectJobLogListByNode(String executeNode);
    
    /**
     * 统计任务执行情况
     * 
     * @param jobId 任务ID
     * @return 统计信息
     */
    Map<String, Object> selectJobExecuteStatistics(Long jobId);
    
    /**
     * 统计所有任务执行情况
     * 
     * @return 统计信息
     */
    Map<String, Object> selectAllJobExecuteStatistics();
    
    /**
     * 查询任务执行趋势
     * 
     * @param jobId 任务ID
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectJobExecuteTrend(Long jobId, int days);
    
    /**
     * 查询所有任务执行趋势
     * 
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectAllJobExecuteTrend(int days);
    
    /**
     * 查询任务执行性能统计
     * 
     * @param jobId 任务ID
     * @return 性能统计
     */
    Map<String, Object> selectJobPerformanceStatistics(Long jobId);
    
    /**
     * 查询任务执行性能排行
     * 
     * @param limit 限制数量
     * @return 性能排行
     */
    List<Map<String, Object>> selectJobPerformanceRanking(int limit);
    
    /**
     * 查询任务执行错误统计
     * 
     * @param days 天数
     * @return 错误统计
     */
    List<Map<String, Object>> selectJobErrorStatistics(int days);
    
    /**
     * 查询任务执行节点统计
     * 
     * @return 节点统计
     */
    List<Map<String, Object>> selectJobNodeStatistics();
    
    /**
     * 查询任务执行时间分布
     * 
     * @param jobId 任务ID
     * @return 时间分布
     */
    List<Map<String, Object>> selectJobExecuteTimeDistribution(Long jobId);
    
    /**
     * 查询系统资源使用统计
     * 
     * @param days 天数
     * @return 资源使用统计
     */
    List<Map<String, Object>> selectSystemResourceStatistics(int days);
    
    /**
     * 清理过期的执行日志
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredJobLog(int days);
    
    /**
     * 清理指定任务的过期执行日志
     * 
     * @param jobId 任务ID
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredJobLogByJobId(Long jobId, int days);
    
    /**
     * 查询执行日志总数
     * 
     * @return 总数
     */
    long selectJobLogCount();
    
    /**
     * 查询指定任务的执行日志总数
     * 
     * @param jobId 任务ID
     * @return 总数
     */
    long selectJobLogCountByJobId(Long jobId);
    
    /**
     * 查询指定状态的执行日志总数
     * 
     * @param status 状态
     * @return 总数
     */
    long selectJobLogCountByStatus(String status);
    
    /**
     * 查询今日执行日志总数
     * 
     * @return 总数
     */
    long selectTodayJobLogCount();
    
    /**
     * 查询今日成功执行日志总数
     * 
     * @return 总数
     */
    long selectTodaySuccessJobLogCount();
    
    /**
     * 查询今日失败执行日志总数
     * 
     * @return 总数
     */
    long selectTodayFailureJobLogCount();
    
    /**
     * 查询平均执行时间
     * 
     * @param jobId 任务ID
     * @return 平均执行时间
     */
    Long selectAvgExecuteTime(Long jobId);
    
    /**
     * 查询最大执行时间
     * 
     * @param jobId 任务ID
     * @return 最大执行时间
     */
    Long selectMaxExecuteTime(Long jobId);
    
    /**
     * 查询最小执行时间
     * 
     * @param jobId 任务ID
     * @return 最小执行时间
     */
    Long selectMinExecuteTime(Long jobId);
    
    /**
     * 查询任务成功率
     * 
     * @param jobId 任务ID
     * @return 成功率
     */
    Double selectJobSuccessRate(Long jobId);
    
    /**
     * 查询任务最近执行状态
     * 
     * @param jobId 任务ID
     * @param limit 限制数量
     * @return 执行状态列表
     */
    List<String> selectRecentJobExecuteStatus(Long jobId, int limit);
    
    /**
     * 更新执行日志结束时间和执行时间
     * 
     * @param logId 日志ID
     * @param endTime 结束时间
     * @param executeTime 执行时间
     * @return 结果
     */
    int updateJobLogEndTime(Long logId, Date endTime, Long executeTime);
    
    /**
     * 更新执行日志状态和结果
     * 
     * @param logId 日志ID
     * @param status 状态
     * @param executeResult 执行结果
     * @param exceptionInfo 异常信息
     * @return 结果
     */
    int updateJobLogResult(Long logId, String status, String executeResult, String exceptionInfo);
    
    /**
     * 创建任务执行开始日志
     * 
     * @param jobId 任务ID
     * @param jobName 任务名称
     * @param jobGroup 任务组名
     * @param invokeTarget 调用目标
     * @param jobParams 任务参数
     * @return 日志ID
     */
    Long createJobStartLog(Long jobId, String jobName, String jobGroup, String invokeTarget, String jobParams);
    
    /**
     * 完成任务执行日志
     * 
     * @param logId 日志ID
     * @param status 执行状态
     * @param executeResult 执行结果
     * @param exceptionInfo 异常信息
     * @param processedRecords 处理记录数
     * @param successRecords 成功记录数
     * @param failureRecords 失败记录数
     * @return 结果
     */
    int completeJobLog(Long logId, String status, String executeResult, String exceptionInfo,
                      Long processedRecords, Long successRecords, Long failureRecords);
    
    /**
     * 导出执行日志
     * 
     * @param logIds 日志ID数组
     * @return 导出数据
     */
    String exportJobLog(Long[] logIds);
    
    /**
     * 生成执行日志报告
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报告数据
     */
    Map<String, Object> generateJobLogReport(Date startTime, Date endTime);
    
    /**
     * 获取任务执行健康度评分
     * 
     * @param jobId 任务ID
     * @return 健康度评分（0-100）
     */
    int getJobHealthScore(Long jobId);
    
    /**
     * 获取系统整体健康度评分
     * 
     * @return 健康度评分（0-100）
     */
    int getSystemHealthScore();
    
    /**
     * 预测任务执行时间
     * 
     * @param jobId 任务ID
     * @return 预测执行时间（毫秒）
     */
    Long predictJobExecuteTime(Long jobId);
    
    /**
     * 分析任务执行异常模式
     * 
     * @param jobId 任务ID
     * @param days 分析天数
     * @return 异常模式分析结果
     */
    Map<String, Object> analyzeJobExceptionPattern(Long jobId, int days);
}
