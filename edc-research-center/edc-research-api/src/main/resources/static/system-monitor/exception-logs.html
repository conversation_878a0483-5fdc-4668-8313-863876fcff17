<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常日志监控</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .page-header {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }
        .table th {
            font-weight: 600;
            color: #2c3e50;
            border-top: none;
        }
        .search-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .level-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        .level-error { background-color: #ffebee; color: #c62828; }
        .level-warn { background-color: #fff8e1; color: #f57f17; }
        .level-info { background-color: #e3f2fd; color: #1565c0; }
        .level-debug { background-color: #f3e5f5; color: #7b1fa2; }
        .token-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .token-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .logout-btn {
            width: 100%;
            padding: 8px;
            font-size: 12px;
        }
        .exception-detail {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .stack-trace {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
        }

        /* 异常消息突出显示样式 */
        .exception-message-container {
            background-color: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .exception-message-label {
            color: #c53030;
            font-weight: 700;
            font-size: 14px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .exception-message-label i {
            margin-right: 8px;
            font-size: 16px;
        }

        .exception-message-content {
            background-color: #ffffff;
            border: 1px solid #fc8181;
            border-radius: 6px;
            padding: 15px;
            color: #c53030;
            font-weight: 600;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
            box-shadow: inset 0 2px 4px rgba(197, 48, 48, 0.1);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .exception-message-content:empty::before {
            content: "暂无异常消息...";
            color: #a0aec0;
            font-style: italic;
            font-weight: normal;
        }
    </style>
</head>
<body>


    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="h3 mb-0">
                        <i class="fa fa-exclamation-triangle text-warning"></i> 异常日志监控
                    </h1>
                    <p class="text-muted mb-0">监控系统异常和错误日志</p>
                </div>
                <div class="col-auto d-flex align-items-center">
                    <!-- Token状态显示 -->
                    <div id="tokenStatus" class="mr-3">
                        <div class="d-flex align-items-center bg-light rounded px-3 py-2">
                            <div class="mr-2">
                                <span id="token-status" class="badge badge-success">Token有效</span>
                            </div>
                            <div class="mr-2">
                                <small class="text-muted">剩余: <span id="token-remaining-time">--</span></small>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="logout()">
                                <i class="fa fa-sign-out"></i> 退出
                            </button>
                        </div>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="navigateToPage('index.html')">
                            <i class="fa fa-home"></i> 首页
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('access-logs.html')">
                            <i class="fa fa-list-alt"></i> 访问日志
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('online-users.html')">
                            <i class="fa fa-users"></i> 在线用户
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-danger" id="todayErrors">--</h3>
                        <p class="text-muted mb-0">今日错误</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning" id="todayWarnings">--</h3>
                        <p class="text-muted mb-0">今日警告</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info" id="recentErrors">--</h3>
                        <p class="text-muted mb-0">最近1小时错误</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success" id="errorRate">--</h3>
                        <p class="text-muted mb-0">错误率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>异常类型</label>
                            <input type="text" class="form-control" id="exceptionType" placeholder="输入异常类型">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>异常消息</label>
                            <input type="text" class="form-control" id="exceptionMessage" placeholder="输入异常消息关键词">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>日志级别</label>
                            <select class="form-control" id="logLevel">
                                <option value="">全部</option>
                                <option value="ERROR">ERROR</option>
                                <option value="WARN">WARN</option>
                                <option value="INFO">INFO</option>
                                <option value="DEBUG">DEBUG</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>请求路径</label>
                            <input type="text" class="form-control" id="requestUrl" placeholder="输入请求路径">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-outline-secondary ml-2" onclick="resetSearch()">
                                    <i class="fa fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">异常日志列表</h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportData()">
                            <i class="fa fa-download"></i> 导出
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearOldLogs()">
                            <i class="fa fa-trash"></i> 清理旧日志
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="alertContainer"></div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>发生时间</th>
                                <th>级别</th>
                                <th>异常类型</th>
                                <th>异常消息</th>
                                <th>请求路径</th>
                                <th>用户ID</th>
                                <th>IP地址</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载数据...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <span class="text-muted">每页显示</span>
                    <select class="form-control form-control-sm mx-2" style="width: auto;" id="pageSize" onchange="changePageSize()">
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-muted">条记录</span>
                </div>
            </div>
            <div class="col-md-6">
                <nav>
                    <ul class="pagination justify-content-end mb-0" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 异常详情模态框 -->
    <div class="modal fade" id="exceptionDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">异常详情</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>异常类型:</strong> <span id="detailExceptionType">--</span>
                        </div>
                        <div class="col-md-6">
                            <strong>发生时间:</strong> <span id="detailCreateTime">--</span>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>请求路径:</strong> <span id="detailRequestUrl">--</span>
                        </div>
                        <div class="col-md-6">
                            <strong>用户ID:</strong> <span id="detailUserId">--</span>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>IP地址:</strong> <span id="detailIpAddress">--</span>
                        </div>
                        <div class="col-md-6">
                            <strong>日志级别:</strong> <span id="detailLogLevel">--</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="exception-message-container">
                            <div class="exception-message-label">
                                <i class="fa fa-exclamation-triangle"></i>
                                异常消息 (重点关注)
                            </div>
                            <div class="exception-message-content" id="detailExceptionMessage"></div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>堆栈跟踪:</strong>
                        <div class="mt-2 stack-trace" id="detailStackTrace">
                            --
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="copyExceptionDetail()">复制详情</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script>
        let currentPage = 1;
        let currentPageSize = 20;
        let totalPages = 0;
        let totalRecords = 0;
        let currentExceptionDetail = null;

        $(document).ready(function() {
            // 检查登录状态
            if (!checkLoginStatus()) {
                return;
            }

            // 初始化Token状态显示
            updateTokenStatus();
            // 每秒更新Token状态
            setInterval(updateTokenStatus, 1000);

            // 立即加载数据
            setTimeout(() => {
                loadExceptionLogs();
                loadStatistics();
            }, 100); // 延迟100ms确保页面完全加载

            // 定时刷新数据
            setInterval(() => {
                loadExceptionLogs();
                loadStatistics();
            }, 60000); // 1分钟刷新一次

            // 搜索表单提交
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                currentPage = 1;
                loadExceptionLogs();
            });
        });

        // 加载异常日志数据
        async function loadExceptionLogs() {
            try {
                showLoading();
                
                const params = {
                    pageNum: currentPage,
                    pageSize: currentPageSize,
                    exceptionType: $('#exceptionType').val().trim(),
                    exceptionMessage: $('#exceptionMessage').val().trim(),
                    logLevel: $('#logLevel').val(),
                    requestUrl: $('#requestUrl').val().trim()
                };

                const url = `${getApiBaseUrl()}/monitor/exception-logs/list`;
                const response = await makeApiRequest(url, {
                    method: 'GET',
                    data: params
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    displayExceptionLogs(result.data);
                    showAlert('alertContainer', '数据加载成功', 'success');
                } else {
                    showAlert('alertContainer', result.message || '加载数据失败', 'danger');
                }
            } catch (error) {
                console.error('加载异常日志失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/exception-logs/statistics`);
                const result = await response.json();
                
                if (result.code === 200 && result.data) {
                    const data = result.data;
                    $('#todayErrors').text(data.todayErrors || 0);
                    $('#todayWarnings').text(data.todayWarnings || 0);
                    $('#recentErrors').text(data.recentErrors || 0);
                    $('#errorRate').text((data.errorRate || 0).toFixed(2) + '%');
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }
            }
        }

        // 显示异常日志数据
        function displayExceptionLogs(data) {
            const tbody = $('#dataTableBody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                tbody.html(`
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fa fa-check-circle text-success" style="font-size: 48px;"></i>
                            <div class="mt-2 text-muted">暂无异常日志</div>
                        </td>
                    </tr>
                `);
                return;
            }

            totalPages = data.totalPage || 0;
            totalRecords = data.total || 0;

            data.list.forEach(log => {
                const row = `
                    <tr>
                        <td>${formatDateTime(log.createTime)}</td>
                        <td><span class="level-badge level-${(log.logLevel || 'info').toLowerCase()}">${log.logLevel || 'INFO'}</span></td>
                        <td title="${log.exceptionType || '-'}">${truncateText(log.exceptionType || '-', 20)}</td>
                        <td title="${log.exceptionMessage || '-'}" class="exception-detail">${truncateText(log.exceptionMessage || '-', 40)}</td>
                        <td title="${log.requestUrl || '-'}">${truncateText(log.requestUrl || '-', 30)}</td>
                        <td>${log.userId || '-'}</td>
                        <td>${log.ipAddress || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewExceptionDetail('${log.idStr || log.id}')">
                                <i class="fa fa-eye"></i> 详情
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            updatePagination();
        }

        // 截断文本
        function truncateText(text, maxLength) {
            if (!text || text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // 显示加载状态
        function showLoading() {
            $('#dataTableBody').html(`
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载数据...</div>
                    </td>
                </tr>
            `);
        }

        // 更新分页
        function updatePagination() {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="javascript:void(0)" onclick="changePage(${currentPage - 1})">上一页</a>
                </li>
            `);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.append(`
                    <li class="page-item ${active}">
                        <a class="page-link" href="javascript:void(0)" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="javascript:void(0)" onclick="changePage(${currentPage + 1})">下一页</a>
                </li>
            `);
        }

        // 切换页码
        function changePage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            currentPage = page;
            loadExceptionLogs();
        }

        // 改变页面大小
        function changePageSize() {
            currentPageSize = parseInt($('#pageSize').val());
            currentPage = 1;
            loadExceptionLogs();
        }

        // 重置搜索
        function resetSearch() {
            $('#searchForm')[0].reset();
            currentPage = 1;
            loadExceptionLogs();
        }

        // 刷新数据
        function refreshData() {
            loadExceptionLogs();
            loadStatistics();
        }

        // 查看异常详情
        async function viewExceptionDetail(logId) {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/exception-logs/detail/${logId}`);
                const result = await response.json();
                
                if (result.code === 200 && result.data) {
                    currentExceptionDetail = result.data;
                    showExceptionDetailModal(result.data);
                } else {
                    showAlert('alertContainer', result.message || '获取异常详情失败', 'danger');
                }
            } catch (error) {
                console.error('获取异常详情失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 显示异常详情模态框
        function showExceptionDetailModal(data) {
            $('#detailExceptionType').text(data.exceptionType || '--');
            $('#detailCreateTime').text(formatDateTime(data.createTime));
            $('#detailRequestUrl').text(data.requestUrl || '--');
            $('#detailUserId').text(data.userId || '--');
            $('#detailIpAddress').text(data.ipAddress || '--');
            $('#detailLogLevel').text(data.logLevel || '--');
            $('#detailExceptionMessage').text(data.exceptionMessage || '');
            $('#detailStackTrace').text(data.stackTrace || '--');
            
            $('#exceptionDetailModal').modal('show');
        }

        // 复制异常详情
        function copyExceptionDetail() {
            if (!currentExceptionDetail) return;
            
            const detail = `
异常类型: ${currentExceptionDetail.exceptionType || '--'}
发生时间: ${formatDateTime(currentExceptionDetail.createTime)}
请求路径: ${currentExceptionDetail.requestUrl || '--'}
用户ID: ${currentExceptionDetail.userId || '--'}
IP地址: ${currentExceptionDetail.ipAddress || '--'}
日志级别: ${currentExceptionDetail.logLevel || '--'}
异常消息: ${currentExceptionDetail.exceptionMessage || '--'}
堆栈跟踪:
${currentExceptionDetail.stackTrace || '--'}
            `.trim();
            
            navigator.clipboard.writeText(detail).then(() => {
                showAlert('alertContainer', '异常详情已复制到剪贴板', 'success');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = detail;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('alertContainer', '异常详情已复制到剪贴板', 'success');
            });
        }

        // 清理旧日志
        async function clearOldLogs() {
            const days = prompt('请输入要保留的天数（超过此天数的日志将被清理）:', '30');
            if (!days || isNaN(days) || parseInt(days) < 1) return;
            
            if (!confirm(`确定要清理 ${days} 天前的异常日志吗？此操作不可恢复！`)) return;
            
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/exception-logs/clean`, {
                    method: 'POST',
                    body: JSON.stringify({ retentionDays: parseInt(days) })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert('alertContainer', `已清理 ${result.data || 0} 条旧日志记录`, 'success');
                    loadExceptionLogs();
                    loadStatistics();
                } else {
                    showAlert('alertContainer', result.message || '清理失败', 'danger');
                }
            } catch (error) {
                console.error('清理旧日志失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 更新Token状态显示
        function updateTokenStatus() {
            const statusElement = $('#token-status');
            const timeElement = $('#token-remaining-time');
            const tokenStatusDiv = $('#tokenStatus');

            if (statusElement.length) {
                const isExpired = TokenManager.isTokenExpired();
                statusElement.text(isExpired ? '已过期' : 'Token有效');
                statusElement.removeClass('badge-success badge-danger').addClass(isExpired ? 'badge-danger' : 'badge-success');
            }

            if (timeElement.length) {
                timeElement.text(TokenManager.formatRemainingTime());
            }

            // 显示token状态区域
            if (tokenStatusDiv.length) {
                tokenStatusDiv.show();
            }
        }

        // 导出数据
        function exportData() {
            // TODO: 实现数据导出功能
            alert('数据导出功能待实现');
        }
    </script>
</body>
</html>
